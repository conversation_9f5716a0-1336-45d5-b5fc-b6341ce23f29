# 镇街表数据汇总脚本使用指南

## 快速开始

### 1. 准备工作

确保已安装必要的Python库：
```bash
pip install pandas openpyxl
```

### 2. 准备数据文件

1. 创建 `data` 目录（如果不存在）
2. 将25个镇街表Excel文件放入 `data` 目录
3. 建议文件名包含镇街名称，例如：
   - `江高镇街表.xlsx`
   - `白云区街表.xlsx`
   - `石井街道表.xlsx`

### 3. 选择合适的处理脚本

**如果Excel文件包含合并单元格（推荐）：**
```bash
python3 batch_process_merged.py
```

**如果Excel文件没有合并单元格：**
```bash
python3 batch_process.py
```

脚本会自动：
- 检测 `data` 目录中的所有Excel文件
- 显示文件列表和大小
- 询问是否开始处理
- 逐个处理文件并显示进度
- 自动处理合并单元格（如使用merged版本）
- 生成汇总表

## 脚本功能说明

### 主要脚本

1. **`process_town_data.py`** - 基础核心处理类
   - `TownDataProcessor` 类包含基本处理逻辑
   - 支持自动表头识别
   - 支持多日期列数据提取
   - 支持镇街名称自动提取

2. **`process_merged_cells.py`** - 合并单元格处理类（推荐）
   - `MergedCellProcessor` 类专门处理合并单元格
   - 自动检测和拆分合并单元格
   - 填充合并单元格的值到所有相关单元格
   - 处理完成后自动清理临时文件

3. **`batch_process.py`** - 基础批处理脚本
   - 适用于没有合并单元格的Excel文件
   - 用户友好的交互界面
   - 进度显示和错误处理

4. **`batch_process_merged.py`** - 合并单元格批处理脚本（推荐）
   - 适用于包含合并单元格的Excel文件
   - 自动处理3000+个合并单元格
   - 完整的进度显示和统计信息
   - 结果预览和数据验证

5. **`test_processor.py`** - 测试脚本
   - 使用示例文件测试功能
   - 验证处理逻辑正确性

### 数据处理流程

1. **文件读取**
   - 自动检测表头位置
   - 处理多级表头结构
   - 提取镇街名称

2. **数据提取**
   - 识别日期列（Excel数字格式）
   - 提取每个日期的监测数据
   - 清理和标准化数据

3. **数据汇总**
   - 按居委和环境类型分组
   - 计算各项指标总和
   - 计算BI汇总和成蚊汇总

4. **结果输出**
   - 生成标准格式汇总表
   - 包含完整的统计信息

## 输出格式

### 汇总表列说明

| 列名 | 说明 | 计算方式 |
|------|------|----------|
| 镇街 | 镇街名称 | 从文件名或内容提取 |
| 居委 | 居委会名称 | 直接提取 |
| 环境类型 | 监测环境类型 | 直接提取 |
| 监测户数 | 总监测户数 | 各日期数据求和 |
| 阳性数 | 总阳性数 | 各日期数据求和 |
| 户外布雷图指数（BI） | 总BI指数 | 各日期数据求和 |
| 总雌蚊密度（雌性ADI） | 总雌蚊密度 | 各日期数据求和 |
| BI汇总 | BI汇总指标 | 户外布雷图指数（BI）÷监测户数×100 |
| 成蚊汇总 | 成蚊汇总指标 | 总雌蚊密度（雌性ADI）的值 |

### 支持的环境类型

- 居民区
- 公园景区
- 医疗机构
- 学校
- 福利机构
- 建筑工地
- 闲置房屋
- 商贸区
- 其他

## 合并单元格处理

### 重要说明

大多数镇街表Excel文件都使用了合并单元格，特别是：
- **序号列**: 每个居委对应多个环境类型，序号被合并
- **监测类型列**: 同一监测类型跨多行合并
- **居委列**: 同一居委的不同环境类型合并显示

### 处理过程

使用 `batch_process_merged.py` 时，脚本会：

1. **检测合并单元格**: 自动识别所有合并单元格范围
2. **拆分合并单元格**: 将合并的单元格拆分为独立单元格
3. **填充数据**: 将合并单元格的值填充到所有相关单元格
4. **处理数据**: 按正常流程处理填充后的数据
5. **清理临时文件**: 自动删除处理过程中的临时文件

### 处理效果

- **原始数据**: 45行（只有居民区数据，其他环境类型显示为空）
- **处理后数据**: 405行（45个居委 × 9种环境类型）
- **有效数据**: 约19行（实际有监测数据的记录）

## 常见问题

### Q1: 脚本提示找不到表头怎么办？

**A:** 检查Excel文件格式，确保包含以下列：

- 序号
- 监测类型
- 居委
- 环境类型

### Q2: 镇街名称显示为"未知镇街"怎么办？

**A:**

1. 确保文件名包含镇街名称（如"江高镇"、"白云区"等）
2. 或在Excel文件前几行包含镇街信息

### Q3: 处理后数据量不对怎么办？

**A:**

1. 检查原始文件是否有空行或格式问题
2. 查看控制台输出的警告信息
3. 使用 `test_processor.py` 单独测试问题文件

### Q4: 合并单元格处理时间很长怎么办？

**A:**

1. 这是正常现象，处理3000+个合并单元格需要时间
2. 请耐心等待，不要中断处理过程
3. 可以通过控制台输出监控处理进度

### Q5: 如何处理特殊格式的文件？

**A:**

1. 确保文件是标准Excel格式（.xlsx）
2. 检查表头是否在前10行内
3. 必要时手动调整文件格式

## 性能优化

### 处理大量文件

- 脚本支持处理数千行数据的文件
- 内存使用经过优化
- 支持25个文件同时处理

### 提高处理速度

1. 确保文件格式标准化
2. 避免在Excel中打开文件时运行脚本
3. 使用SSD存储提高I/O性能

## 错误处理

脚本包含完整的错误处理机制：

1. **文件级错误**：单个文件失败不影响其他文件
2. **数据级错误**：自动跳过有问题的数据行
3. **格式错误**：提供详细的错误信息和建议

## 日志信息

脚本会输出详细的处理日志：
- 文件读取状态
- 数据提取进度
- 错误和警告信息
- 处理结果统计

## 验证结果

处理完成后建议：
1. 检查汇总表行数是否合理
2. 验证镇街和居委信息是否正确
3. 抽查几个数据点确认计算正确性
4. 对比原始数据确认数据完整性

## 技术支持

如遇到问题：
1. 查看控制台输出的详细错误信息
2. 检查文件格式是否符合要求
3. 使用测试脚本验证单个文件
4. 确认Python环境和依赖库版本
