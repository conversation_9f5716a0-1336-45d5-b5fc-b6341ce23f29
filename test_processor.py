#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试镇街表数据处理脚本
"""

import pandas as pd
import numpy as np
from process_town_data import TownDataProcessor
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_with_sample_files():
    """
    使用示例文件测试处理逻辑
    """
    processor = TownDataProcessor()
    
    # 测试读取镇街表示例
    print("=== 测试读取镇街表示例 ===")
    df_town, town_name = processor.read_town_file('/Users/<USER>/dev/wjj01/samples/镇街表示例.xlsx')

    if df_town is not None:
        print(f"成功读取镇街表，行数: {len(df_town)}, 镇街: {town_name}")
        print(f"列名: {df_town.columns.tolist()[:10]}")
        print("前5行数据:")
        print(df_town.head())
        
        # 提取基本信息
        basic_info = processor.extract_basic_info(df_town)
        if basic_info is not None:
            print(f"\n基本信息提取成功，行数: {len(basic_info)}")
            print("基本信息:")
            print(basic_info.head(10))
        
        # 查找日期列
        date_columns = processor.find_date_columns(df_town)
        print(f"\n找到的日期列: {date_columns}")
        
        # 提取监测数据
        monitoring_data = processor.extract_monitoring_data(df_town, date_columns, town_name)
        if monitoring_data is not None:
            print(f"\n监测数据提取成功，行数: {len(monitoring_data)}")
            print("监测数据:")
            print(monitoring_data.head(10))
            
            # 测试汇总
            summary_df = processor.aggregate_data([monitoring_data])
            if summary_df is not None:
                print(f"\n汇总成功，行数: {len(summary_df)}")
                print("汇总结果:")
                print(summary_df)
                
                # 保存测试结果
                summary_df.to_excel('测试汇总结果.xlsx', index=False)
                print("\n测试汇总结果已保存到: 测试汇总结果.xlsx")
    
    # 测试读取汇总表示例（作为参考）
    print("\n=== 参考汇总表示例 ===")
    try:
        df_summary_ref = pd.read_excel('/Users/<USER>/dev/wjj01/samples/汇总表示例.xlsx', header=1, skiprows=[2])
        print("参考汇总表:")
        print(df_summary_ref.head(10))
    except Exception as e:
        print(f"读取参考汇总表失败: {e}")

def analyze_data_structure():
    """
    详细分析数据结构
    """
    print("=== 详细分析镇街表数据结构 ===")
    
    # 读取原始数据
    df_raw = pd.read_excel('/Users/<USER>/dev/wjj01/samples/镇街表示例.xlsx', header=None, nrows=20)
    
    print("原始数据前20行:")
    for i in range(20):
        row_data = [str(x)[:15] for x in df_raw.iloc[i, :10]]  # 只显示前10列，每个值最多15字符
        print(f"第{i:2d}行: {row_data}")
    
    print("\n=== 分析列结构 ===")
    # 使用第3行作为表头读取
    df = pd.read_excel('/Users/<USER>/dev/wjj01/samples/镇街表示例.xlsx', header=3, nrows=50)
    
    print(f"总列数: {len(df.columns)}")
    print("所有列名:")
    for i, col in enumerate(df.columns):
        print(f"第{i:3d}列: {col}")
        if i > 20:  # 只显示前20列
            print("...")
            break
    
    # 分析数据类型和内容
    print("\n=== 数据内容分析 ===")
    print("基本字段数据:")
    basic_cols = ['序号', '监测类型', '居委', '环境类型']
    for col in basic_cols:
        if col in df.columns:
            unique_values = df[col].dropna().unique()[:10]  # 只显示前10个唯一值
            print(f"{col}: {unique_values}")

if __name__ == "__main__":
    # 先分析数据结构
    analyze_data_structure()
    
    print("\n" + "="*80 + "\n")
    
    # 然后测试处理逻辑
    test_with_sample_files()
