#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dashboard统计表更新脚本
从最新的汇总表中读取数据并填入dashboard.xlsx
"""

import pandas as pd
import numpy as np
import os
import glob
import openpyxl
from openpyxl import load_workbook
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DashboardUpdater:
    def __init__(self):
        self.dashboard_file = '/Users/<USER>/dev/wjj01/samples/dashboard.xlsx'
        self.summary_data = None
        
    def find_latest_summary_file(self):
        """
        查找最新的汇总表文件
        """
        # 查找所有汇总表文件
        patterns = [
            "汇总表_含风险等级_*.xlsx",
            "汇总表_所有日期_*.xlsx", 
            "汇总表_新结构_*.xlsx",
            "汇总表_*.xlsx"
        ]
        
        all_files = []
        for pattern in patterns:
            files = glob.glob(pattern)
            all_files.extend(files)
        
        if not all_files:
            logger.error("没有找到汇总表文件")
            return None
        
        # 选择最新的文件（按修改时间）
        latest_file = max(all_files, key=os.path.getmtime)
        logger.info(f"找到最新汇总表文件: {latest_file}")
        return latest_file
    
    def load_summary_data(self, file_path):
        """
        加载汇总表数据
        """
        try:
            df = pd.read_excel(file_path)
            logger.info(f"成功加载汇总表数据: {len(df)} 行")
            
            # 显示数据概览
            print(f"汇总表数据概览:")
            print(f"  总行数: {len(df)}")
            print(f"  镇街数量: {df['镇街'].nunique()}")
            print(f"  居委数量: {df['居委'].nunique()}")
            print(f"  环境类型数量: {df['环境类型'].nunique()}")
            
            if '日期' in df.columns:
                print(f"  日期数量: {df['日期'].nunique()}")
                dates = sorted(df['日期'].unique())
                print(f"  日期范围: {dates[0]} 到 {dates[-1]}")
            
            # 有效数据统计
            if '监测户数' in df.columns:
                valid_data = df[df['监测户数'] > 0]
                print(f"  有效监测数据: {len(valid_data)} 行")
            
            self.summary_data = df
            return True
            
        except Exception as e:
            logger.error(f"加载汇总表数据失败: {e}")
            return False
    
    def get_dashboard_sheets(self):
        """
        获取dashboard中的所有镇街sheet
        """
        try:
            wb = load_workbook(self.dashboard_file)
            sheet_names = wb.sheetnames
            logger.info(f"Dashboard包含 {len(sheet_names)} 个sheet: {sheet_names}")
            return sheet_names
        except Exception as e:
            logger.error(f"读取dashboard文件失败: {e}")
            return []
    
    def analyze_dashboard_structure(self, sheet_name):
        """
        分析dashboard中指定sheet的结构
        """
        try:
            df = pd.read_excel(self.dashboard_file, sheet_name=sheet_name, header=None, nrows=20)
            
            # 查找表头行（包含'居委'、'环境类型'的行）
            header_row = None
            for i in range(20):
                if i >= len(df):
                    break
                row_values = [str(x) for x in df.iloc[i, :6]]
                if '居委' in row_values and '环境类型' in row_values:
                    header_row = i
                    break
            
            if header_row is None:
                logger.warning(f"Sheet '{sheet_name}' 中找不到表头行")
                return None
            
            # 查找数据列的位置
            header_data = df.iloc[header_row, :]
            col_mapping = {}
            for i, val in enumerate(header_data):
                val_str = str(val).strip()
                if val_str == '居委':
                    col_mapping['居委_col'] = i
                elif val_str == '环境类型':
                    col_mapping['环境类型_col'] = i
                elif '45869' in val_str or '监测户数' in val_str:  # 日期列或监测户数列
                    # 查找子表头
                    if header_row + 1 < len(df):
                        sub_header = df.iloc[header_row + 1, :]
                        for j in range(i, min(i + 10, len(sub_header))):
                            sub_val = str(sub_header.iloc[j]).strip()
                            if '监测户数' in sub_val:
                                col_mapping['监测户数_col'] = j
                            elif '阳性数' in sub_val:
                                col_mapping['阳性数_col'] = j
                            elif '户外布雷图指数' in sub_val or 'BI' in sub_val:
                                col_mapping['BI_col'] = j
                            elif '总雌蚊密度' in sub_val or 'ADI' in sub_val:
                                col_mapping['ADI_col'] = j
            
            col_mapping['header_row'] = header_row
            col_mapping['data_start_row'] = header_row + 2  # 跳过表头和子表头
            
            logger.info(f"Sheet '{sheet_name}' 结构分析: {col_mapping}")
            return col_mapping
            
        except Exception as e:
            logger.error(f"分析sheet '{sheet_name}' 结构失败: {e}")
            return None
    
    def get_summary_data_for_town(self, town_name, date=None):
        """
        获取指定镇街的汇总数据
        """
        if self.summary_data is None:
            return None

        # 首先尝试精确匹配
        town_data = self.summary_data[self.summary_data['镇街'] == town_name]

        if len(town_data) == 0:
            # 尝试包含匹配（dashboard中的名称可能是简化版）
            town_data = self.summary_data[self.summary_data['镇街'].str.contains(town_name, na=False)]

        if len(town_data) == 0:
            # 尝试反向匹配（汇总表中的名称可能包含dashboard中的名称）
            for town in self.summary_data['镇街'].unique():
                if town_name in str(town):
                    town_data = self.summary_data[self.summary_data['镇街'] == town]
                    logger.info(f"镇街名称匹配: '{town_name}' -> '{town}'")
                    break

        if len(town_data) == 0:
            # 最后尝试模糊匹配
            for town in self.summary_data['镇街'].unique():
                town_clean = str(town).replace('街', '').replace('镇', '').strip()
                if town_name == town_clean or town_clean in town_name:
                    town_data = self.summary_data[self.summary_data['镇街'] == town]
                    logger.info(f"镇街名称模糊匹配: '{town_name}' -> '{town}'")
                    break

        if len(town_data) == 0:
            logger.warning(f"没有找到镇街 '{town_name}' 的数据")
            return None

        # 如果有日期列，选择最新日期的数据
        if '日期' in town_data.columns and date is None:
            latest_date = town_data['日期'].max()
            town_data = town_data[town_data['日期'] == latest_date]
            logger.info(f"使用镇街 '{town_name}' 最新日期 {latest_date} 的数据")
        elif date is not None:
            town_data = town_data[town_data['日期'] == date]

        return town_data
    
    def update_dashboard_sheet(self, sheet_name):
        """
        更新dashboard中指定sheet的数据
        """
        try:
            logger.info(f"正在更新sheet: {sheet_name}")
            
            # 分析sheet结构
            structure = self.analyze_dashboard_structure(sheet_name)
            if structure is None:
                return False
            
            # 获取对应镇街的汇总数据
            town_data = self.get_summary_data_for_town(sheet_name)
            if town_data is None or len(town_data) == 0:
                logger.warning(f"没有找到镇街 '{sheet_name}' 的汇总数据")
                return False
            
            # 使用openpyxl更新Excel文件
            wb = load_workbook(self.dashboard_file)
            ws = wb[sheet_name]
            
            # 读取现有数据以确定更新位置
            df_existing = pd.read_excel(self.dashboard_file, sheet_name=sheet_name, header=None)
            
            updated_count = 0
            
            # 遍历现有数据行，查找匹配的居委和环境类型
            for row_idx in range(structure['data_start_row'], len(df_existing)):
                if row_idx >= len(df_existing):
                    break
                
                # 获取居委和环境类型
                juwei_col = structure.get('居委_col')
                env_col = structure.get('环境类型_col')
                
                if juwei_col is None or env_col is None:
                    continue
                
                juwei = df_existing.iloc[row_idx, juwei_col] if juwei_col < len(df_existing.columns) else None
                env_type = df_existing.iloc[row_idx, env_col] if env_col < len(df_existing.columns) else None
                
                if pd.isna(juwei) or pd.isna(env_type):
                    continue
                
                juwei = str(juwei).strip()
                env_type = str(env_type).strip()
                
                # 在汇总数据中查找匹配的记录
                matching_data = town_data[
                    (town_data['居委'].str.strip() == juwei) & 
                    (town_data['环境类型'].str.strip() == env_type)
                ]
                
                if len(matching_data) > 0:
                    record = matching_data.iloc[0]
                    
                    # 更新监测数据
                    excel_row = row_idx + 1  # Excel行号从1开始
                    
                    if '监测户数_col' in structure and '监测户数' in record:
                        col = structure['监测户数_col'] + 1  # Excel列号从1开始
                        ws.cell(row=excel_row, column=col, value=float(record['监测户数']) if not pd.isna(record['监测户数']) else 0)
                    
                    if '阳性数_col' in structure and '阳性数' in record:
                        col = structure['阳性数_col'] + 1
                        ws.cell(row=excel_row, column=col, value=float(record['阳性数']) if not pd.isna(record['阳性数']) else 0)
                    
                    if 'BI_col' in structure and '户外布雷图指数（BI）' in record:
                        col = structure['BI_col'] + 1
                        bi_value = record['户外布雷图指数（BI）']
                        ws.cell(row=excel_row, column=col, value=float(bi_value) if not pd.isna(bi_value) else 0)
                    
                    if 'ADI_col' in structure and '总雌蚊密度（雌性ADI）' in record:
                        col = structure['ADI_col'] + 1
                        adi_value = record['总雌蚊密度（雌性ADI）']
                        ws.cell(row=excel_row, column=col, value=float(adi_value) if not pd.isna(adi_value) else 0)
                    
                    updated_count += 1
                    logger.debug(f"更新 {juwei} - {env_type}: 监测户数={record.get('监测户数', 0)}")
            
            # 保存文件
            output_file = f"dashboard_updated_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            wb.save(output_file)
            logger.info(f"Sheet '{sheet_name}' 更新完成，更新了 {updated_count} 条记录，保存为: {output_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"更新sheet '{sheet_name}' 失败: {e}")
            return False
    
    def update_all_sheets(self):
        """
        更新所有sheet
        """
        # 查找最新汇总表
        latest_file = self.find_latest_summary_file()
        if latest_file is None:
            return False
        
        # 加载汇总数据
        if not self.load_summary_data(latest_file):
            return False
        
        # 获取dashboard的所有sheet
        sheet_names = self.get_dashboard_sheets()
        if not sheet_names:
            return False
        
        print(f"\n开始更新Dashboard统计表...")
        print(f"数据源: {latest_file}")
        print(f"目标文件: {self.dashboard_file}")
        print(f"需要更新的sheet: {len(sheet_names)} 个")
        print("=" * 60)
        
        successful_updates = 0
        failed_updates = []
        
        for i, sheet_name in enumerate(sheet_names, 1):
            print(f"[{i:2d}/{len(sheet_names)}] 更新sheet: {sheet_name}")
            
            if self.update_dashboard_sheet(sheet_name):
                successful_updates += 1
                print(f"    ✅ 更新成功")
            else:
                failed_updates.append(sheet_name)
                print(f"    ❌ 更新失败")
        
        print("=" * 60)
        print(f"更新完成: 成功 {successful_updates} 个，失败 {len(failed_updates)} 个")
        
        if failed_updates:
            print("失败的sheet:")
            for sheet in failed_updates:
                print(f"  - {sheet}")
        
        return successful_updates > 0

def main():
    """
    主函数
    """
    print("Dashboard统计表更新脚本")
    print("=" * 40)
    
    updater = DashboardUpdater()
    
    # 检查dashboard文件是否存在
    if not os.path.exists(updater.dashboard_file):
        print(f"错误: Dashboard文件不存在: {updater.dashboard_file}")
        return
    
    # 更新所有sheet
    success = updater.update_all_sheets()
    
    if success:
        print("\n✅ Dashboard更新完成！")
        print("更新后的文件已保存为: dashboard_updated_YYYYMMDD_HHMMSS.xlsx")
    else:
        print("\n❌ Dashboard更新失败，请检查错误信息")

if __name__ == "__main__":
    main()
