#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理包含合并单元格的镇街表数据
适用于处理25个镇街表文件，自动处理合并单元格
"""

import os
import glob
import pandas as pd
from process_merged_cells import MergedCellProcessor
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_data_directory():
    """
    创建数据目录
    """
    data_dir = "data"
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
        print(f"已创建数据目录: {data_dir}")
        print("请将25个镇街表Excel文件放入此目录")
        return False
    return True

def check_files(data_dir="data"):
    """
    检查数据文件
    """
    pattern = os.path.join(data_dir, "*.xlsx")
    files = glob.glob(pattern)
    
    print(f"在 {data_dir} 目录中找到 {len(files)} 个Excel文件:")
    for i, file in enumerate(files, 1):
        file_name = os.path.basename(file)
        file_size = os.path.getsize(file) / 1024 / 1024  # MB
        print(f"  {i:2d}. {file_name} ({file_size:.1f} MB)")
    
    return files

def process_with_progress(files, output_file="汇总表结果_合并单元格处理.xlsx"):
    """
    带进度显示的处理函数，支持合并单元格处理
    """
    processor = MergedCellProcessor()
    
    print(f"\n开始处理 {len(files)} 个文件...")
    print("注意：处理合并单元格可能需要较长时间")
    print("=" * 60)
    
    all_data = []
    successful_files = 0
    failed_files = []
    
    for i, file_path in enumerate(files, 1):
        file_name = os.path.basename(file_path)
        print(f"[{i:2d}/{len(files)}] 正在处理: {file_name}")
        
        try:
            # 读取文件（包含合并单元格处理）
            df, town_name = processor.read_town_file(file_path)
            if df is None:
                failed_files.append(file_name)
                continue
            
            # 查找日期列
            date_columns = processor.find_date_columns(df)
            print(f"    找到 {len(date_columns)} 个日期列")
            
            # 提取监测数据
            monitoring_data = processor.extract_monitoring_data(df, date_columns, town_name)
            
            if monitoring_data is not None:
                all_data.append(monitoring_data)
                successful_files += 1
                
                # 统计有效数据
                valid_data = monitoring_data[monitoring_data['监测户数'] > 0]
                print(f"    成功提取 {len(monitoring_data)} 行数据，镇街: {town_name}")
                print(f"    其中有效监测数据: {len(valid_data)} 行")
            else:
                failed_files.append(file_name)
                print(f"    警告: 未能提取到有效数据")
                
        except Exception as e:
            failed_files.append(file_name)
            print(f"    错误: {str(e)}")
    
    print("=" * 60)
    print(f"处理完成: 成功 {successful_files} 个，失败 {len(failed_files)} 个")
    
    if failed_files:
        print("失败的文件:")
        for file in failed_files:
            print(f"  - {file}")
    
    if not all_data:
        print("错误: 没有提取到任何有效数据")
        return False
    
    # 汇总数据
    print(f"\n正在汇总数据...")
    summary_df = processor.aggregate_data(all_data)
    
    if summary_df is None:
        print("错误: 数据汇总失败")
        return False
    
    # 保存结果
    try:
        summary_df.to_excel(output_file, index=False)
        print(f"汇总表已保存到: {output_file}")
        print(f"汇总表包含 {len(summary_df)} 行数据")
        
        # 显示汇总统计
        print("\n汇总统计:")
        print(f"  镇街数量: {summary_df['镇街'].nunique()}")
        print(f"  居委数量: {summary_df['居委'].nunique()}")
        print(f"  环境类型数量: {summary_df['环境类型'].nunique()}")
        
        # 显示环境类型分布
        env_counts = summary_df['环境类型'].value_counts()
        print("\n环境类型分布:")
        for env_type, count in env_counts.items():
            print(f"  {env_type}: {count} 条记录")
        
        # 显示各镇街的数据量
        town_counts = summary_df['镇街'].value_counts()
        print("\n各镇街数据量:")
        for town, count in town_counts.items():
            print(f"  {town}: {count} 条记录")
        
        # 显示有效数据统计
        valid_summary = summary_df[summary_df['监测户数'] > 0]
        print(f"\n有效监测数据: {len(valid_summary)} 行")
        
        return True
        
    except Exception as e:
        print(f"保存文件失败: {str(e)}")
        return False

def preview_results(output_file="汇总表结果_合并单元格处理.xlsx"):
    """
    预览结果
    """
    try:
        df = pd.read_excel(output_file)
        print(f"\n汇总表预览 (前10行):")
        print("=" * 120)
        print(df.head(10).to_string(index=False))
        
        print(f"\n数据概览:")
        print(f"  总行数: {len(df)}")
        print(f"  总列数: {len(df.columns)}")
        print(f"  列名: {', '.join(df.columns)}")
        
        # 检查数据完整性
        print(f"\n数据完整性检查:")
        for col in df.columns:
            null_count = df[col].isnull().sum()
            null_pct = null_count / len(df) * 100
            print(f"  {col}: {null_count} 个空值 ({null_pct:.1f}%)")
        
        # 显示有监测数据的记录
        valid_data = df[df['监测户数'] > 0]
        if len(valid_data) > 0:
            print(f"\n有监测数据的记录 (前10行):")
            print(valid_data[['镇街', '居委', '环境类型', '监测户数', '阳性数', 'BI汇总']].head(10).to_string(index=False))
            
    except Exception as e:
        print(f"预览结果失败: {str(e)}")

def main():
    """
    主函数
    """
    print("镇街表数据批量处理脚本 (支持合并单元格)")
    print("=" * 50)
    
    # 检查数据目录
    if not create_data_directory():
        return
    
    # 检查文件
    files = check_files()
    if not files:
        print("错误: 没有找到Excel文件")
        print("请将镇街表Excel文件放入 data/ 目录")
        return
    
    # 确认处理
    print("\n注意事项:")
    print("- 此脚本会自动处理Excel文件中的合并单元格")
    print("- 处理过程可能需要较长时间，请耐心等待")
    print("- 每个文件会生成临时文件用于处理，处理完成后自动删除")
    
    response = input(f"\n是否开始处理这 {len(files)} 个文件? (y/n): ")
    if response.lower() != 'y':
        print("已取消处理")
        return
    
    # 处理文件
    output_file = "汇总表结果_合并单元格处理.xlsx"
    success = process_with_progress(files, output_file)
    
    if success:
        # 预览结果
        preview_results(output_file)
        print(f"\n处理完成！汇总表已保存为: {output_file}")
        print("\n说明:")
        print("- 汇总表包含所有居委的所有环境类型数据")
        print("- 监测户数为0的记录表示该环境类型无监测数据")
        print("- BI汇总 = 户外布雷图指数（BI） ÷ 监测户数 × 100")
        print("- 成蚊汇总 = 总雌蚊密度（雌性ADI）的值")
    else:
        print("\n处理失败，请检查错误信息")

if __name__ == "__main__":
    main()
