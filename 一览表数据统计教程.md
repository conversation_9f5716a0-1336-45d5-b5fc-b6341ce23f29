# 白云区蚊媒监测一览表数据统计教程

## 概述

本教程详细说明如何从汇总表数据统计生成白云区蚊媒监测一览表，包括数据处理流程、统计逻辑和计算方法。

## 1. 数据处理流程

### 1.1 数据源处理
```
源数据（Excel文件）
    ↓
汇总表生成（只保留指定日期）
    ↓
风险等级评估
    ↓
一览表统计填写
```

### 1.2 核心脚本
- `auto_data_processor.py` - 生成汇总表
- `add_risk_levels.py` - 添加风险等级
- `complete_dashboard_filler.py` - 填写一览表

## 2. 汇总表数据结构

### 2.1 关键字段
| 字段名 | 说明 | 示例 |
|--------|------|------|
| 镇街 | 镇街名称 | 三元里街 |
| 居委 | 居委名称 | 松柏居委 |
| 环境类型 | 监测环境 | 物业小区、农贸市场 |
| 监测类型 | 监测频率分类 | 一类（每天1测）、三类（三天1测） |
| 监测户数 | 监测的户数 | 45 |
| 阳性数 | 阳性容器数 | 2 |
| 户外布雷图指数（BI） | BI指数 | 4.44 |
| 总雌蚊密度（雌性ADI） | ADI指数 | 2.0 |
| 风险级别 | 计算得出的风险等级 | 低度风险、中度风险 |

### 2.2 监测类型归类规则
```python
# 归类逻辑
if '一类' in 监测类型 or '每天1测' in 监测类型:
    归类为 → 一类监测
elif '三类' in 监测类型 or '三天1测' in 监测类型:
    归类为 → 三类监测
else:
    归类为 → 二类监测
```

**具体归类**：
- `一类（每天1测）` → 一类监测
- `涉疫（每天1测）` → 一类监测
- `三类（三天1测）` → 三类监测
- `非涉疫（三天1测）` → 三类监测

## 3. 风险等级计算

### 3.1 风险等级标准
```python
if BI >= 20 or ADI >= 10:
    风险级别 = '高度风险'
elif BI >= 5 or ADI >= 5:
    风险级别 = '中度风险'
elif BI > 0 or ADI > 0:
    风险级别 = '低度风险'
else:
    风险级别 = None  # 无风险
```

### 3.2 居委风险等级判定
每个居委的整体风险等级按最高风险记录确定：
```python
if 该居委有任何'高度风险'记录:
    居委风险等级 = '高度风险'
elif 该居委有任何'中度风险'记录:
    居委风险等级 = '中度风险'
elif 该居委有任何'低度风险'记录:
    居委风险等级 = '低度风险'
else:
    居委风险等级 = '无风险'
```

## 4. 一览表统计逻辑

### 4.1 基本统计指标

#### 4.1.1 监测村居数（E列）
```python
监测村居数 = 该镇街的唯一居委数量
```

#### 4.1.2 中高风险占比（F列）
```python
中高风险居委数 = 高度风险居委数 + 中度风险居委数
中高风险占比 = (中高风险居委数 / 总居委数) × 100%
```

#### 4.1.3 风险分布（G-J列）
```python
G列(高风险) = 高度风险居委数
H列(中风险) = 中度风险居委数
I列(低风险) = 低度风险居委数
J列(风险可控) = 无风险居委数
```

#### 4.1.4 监测场所数（K列）
```python
监测场所数 = 该镇街所有监测记录的总数
```

### 4.2 环境类型统计

#### 4.2.1 物业小区统计（M-P列）
```python
# 筛选物业小区数据
物业小区数据 = 汇总表[环境类型.包含('物业小区')]

M列(物业小区总数) = 物业小区数据的居委数量
N列(物业小区中高风险占比) = 物业小区中高风险居委占比
O列(物业小区高风险) = 物业小区高风险居委数
P列(物业小区中风险) = 物业小区中风险居委数
```

#### 4.2.2 其他环境类型
按相同逻辑统计：
- Q-T列：农贸市场
- U-X列：学校
- Y-AB列：公园景区
- AC-AF列：医疗机构
- AG-AJ列：福利机构
- AK-AN列：建筑工地
- AO-AR列：其他场所

## 5. 实际统计示例

### 5.1 三元里街农贸市场统计示例

**汇总表数据**：
```
镇街: 三元里街
环境类型: 农贸市场
居委数: 13个
风险分布: 12个无风险 + 1个中度风险（松柏居委）
```

**一览表结果**：
```
AK21(农贸市场总数): 13
AL21(农贸市场中高风险占比): 7.7% (1/13)
AM21(农贸市场高风险): 0
AN21(农贸市场中风险): 1
```

### 5.2 钟落潭镇整体统计示例

**汇总表数据**：
```
镇街: 钟落潭镇
总居委数: 42个
风险分布: 30个中度风险 + 12个低风险
监测场所数: 378个
```

**一览表结果**：
```
E18(监测村居数): 42
F18(中高风险占比): 54.5% (30/55，按有效数据计算)
G18(高风险): 0
H18(中风险): 30
I18(低风险): 12
J18(风险可控): 0
K18(监测场所数): 378
```

## 6. 特殊处理情况

### 6.1 钟落潭镇数据修正
钟落潭镇的Excel列结构特殊，需要特殊处理：
```python
# 钟落潭镇列映射
日期列+1: 监测户数
日期列+2: BI指数
日期列+3: ADI指数
日期列+4: 风险等级

# 阳性数计算
阳性数 = round(BI指数 × 监测户数 ÷ 100)
```

### 6.2 缺失数据处理
- 如果某镇街在指定日期没有数据，对应行保持空白
- 如果某环境类型没有数据，相关列填写0或0.0%

### 6.3 监测类型分行填写
- 一类监测：填写到起始行
- 二类监测：填写到起始行+1
- 三类监测：填写到起始行+2
- 第81行：跳过不填写

## 7. 数据验证

### 7.1 计算验证
```python
# 验证中高风险占比计算
期望占比 = (高风险数 + 中风险数) / 总居委数 × 100%
实际占比 = F列显示的百分比

if abs(期望占比 - 实际占比) < 0.1:
    print("✅ 计算正确")
else:
    print("❌ 计算有误")
```

### 7.2 数据完整性检查
- 检查24个镇街是否全部填写
- 检查数据是否基于单一日期
- 检查风险占比是否合理（0-100%）
- 检查阳性数是否为整数

## 8. 使用说明

### 8.1 生成指定日期的一览表
```bash
# 1. 生成汇总表（只包含指定日期）
python3 auto_data_processor.py

# 2. 添加风险等级
python3 add_risk_levels.py

# 3. 填写一览表
python3 complete_dashboard_filler.py
```

### 8.2 修改目标日期
在`auto_data_processor.py`中修改：
```python
def main(target_date='2025-07-31'):  # 修改这里的日期
```

## 9. 输出文件

- `汇总表_YYYYMMDD_YYYYMMDD.xlsx` - 只包含指定日期的汇总表
- `汇总表_含风险等级_YYYYMMDD_YYYYMMDD.xlsx` - 含风险等级的汇总表
- `白云区蚊媒监测一览表_完整版_YYYYMMDD.xlsx` - 最终一览表
- `Dashboard填写报告_YYYYMMDD.txt` - 详细填写报告

## 10. 注意事项

1. **数据一致性**：汇总表和一览表必须基于同一日期的数据
2. **监测类型归类**：严格按照"每天1测"和"三天1测"进行归类
3. **风险计算**：基于BI和ADI指数的标准阈值
4. **特殊镇街**：钟落潭镇需要特殊的列映射处理
5. **数据验证**：生成后必须验证计算结果的正确性

## 11. 技术实现细节

### 11.1 核心统计函数

#### 11.1.1 镇街数据统计
```python
def analyze_town_data(df, town_name, target_date):
    """分析单个镇街的数据"""
    # 筛选该镇街指定日期的数据
    town_data = df[(df['镇街'] == town_name) & (df['日期'] == target_date)]

    if len(town_data) == 0:
        return None

    # 统计居委数量
    communities = town_data['居委'].nunique()

    # 按居委统计风险等级
    community_risks = {}
    for community in town_data['居委'].unique():
        community_data = town_data[town_data['居委'] == community]

        # 判断该居委的最高风险等级
        if '高度风险' in community_data['风险级别'].values:
            community_risks[community] = '高度风险'
        elif '中度风险' in community_data['风险级别'].values:
            community_risks[community] = '中度风险'
        elif '低度风险' in community_data['风险级别'].values:
            community_risks[community] = '低度风险'
        else:
            community_risks[community] = '无风险'

    # 统计各风险等级数量
    risk_counts = {
        '高度风险': sum(1 for r in community_risks.values() if r == '高度风险'),
        '中度风险': sum(1 for r in community_risks.values() if r == '中度风险'),
        '低度风险': sum(1 for r in community_risks.values() if r == '低度风险'),
        '无风险': sum(1 for r in community_risks.values() if r == '无风险')
    }

    # 计算中高风险占比
    medium_high = risk_counts['高度风险'] + risk_counts['中度风险']
    risk_pct = (medium_high / communities) * 100 if communities > 0 else 0

    return {
        'communities': communities,
        'high_risk': risk_counts['高度风险'],
        'medium_risk': risk_counts['中度风险'],
        'low_risk': risk_counts['低度风险'],
        'no_risk': risk_counts['无风险'],
        'medium_high_risk_pct': risk_pct,
        'total_sites': len(town_data)
    }
```

#### 11.1.2 环境类型统计
```python
def analyze_environment_data(df, town_name, env_type, target_date):
    """分析特定环境类型的数据"""
    # 筛选数据
    env_data = df[
        (df['镇街'] == town_name) &
        (df['环境类型'].str.contains(env_type, na=False)) &
        (df['日期'] == target_date)
    ]

    if len(env_data) == 0:
        return {'total': 0, 'risk_pct': 0, 'high': 0, 'medium': 0}

    # 按居委统计该环境类型的风险
    community_risks = {}
    for community in env_data['居委'].unique():
        community_env_data = env_data[env_data['居委'] == community]

        # 判断该居委在此环境类型下的风险等级
        if '高度风险' in community_env_data['风险级别'].values:
            community_risks[community] = '高度风险'
        elif '中度风险' in community_env_data['风险级别'].values:
            community_risks[community] = '中度风险'
        elif '低度风险' in community_env_data['风险级别'].values:
            community_risks[community] = '低度风险'
        else:
            community_risks[community] = '无风险'

    # 统计结果
    total = len(community_risks)
    high_count = sum(1 for r in community_risks.values() if r == '高度风险')
    medium_count = sum(1 for r in community_risks.values() if r == '中度风险')
    medium_high_count = high_count + medium_count
    risk_pct = (medium_high_count / total) * 100 if total > 0 else 0

    return {
        'total': total,
        'risk_pct': risk_pct,
        'high': high_count,
        'medium': medium_count
    }
```

### 11.2 Excel填写实现

#### 11.2.1 镇街映射表
```python
# Dashboard中各镇街的起始行号
town_mapping = {
    '江高镇': {'row_start': 9},
    '人和镇': {'row_start': 12},
    '太和镇': {'row_start': 15},
    '钟落潭镇': {'row_start': 18},
    '三元里街': {'row_start': 21},
    '松洲街': {'row_start': 24},
    '景泰街': {'row_start': 27},
    '黄石街': {'row_start': 30},
    '同德街': {'row_start': 33},
    '棠景街': {'row_start': 36},
    '新市街': {'row_start': 39},
    '同和街': {'row_start': 42},
    '京溪街': {'row_start': 45},
    '永平街': {'row_start': 48},
    '均禾街': {'row_start': 51},
    '嘉禾街': {'row_start': 54},
    '石井街': {'row_start': 57},
    '金沙街': {'row_start': 60},
    '云城街': {'row_start': 63},
    '鹤龙街': {'row_start': 66},
    '白云湖街': {'row_start': 69},
    '石门街': {'row_start': 72},
    '龙归街': {'row_start': 75},
    '大源街': {'row_start': 78}
}
```

#### 11.2.2 列映射表
```python
# 环境类型对应的Excel列
environment_columns = {
    '物业小区': {'total': 13, 'risk_pct': 14, 'high': 15, 'medium': 16},  # M, N, O, P
    '农贸市场': {'total': 17, 'risk_pct': 18, 'high': 19, 'medium': 20},  # Q, R, S, T
    '学校': {'total': 21, 'risk_pct': 22, 'high': 23, 'medium': 24},      # U, V, W, X
    '公园景区': {'total': 25, 'risk_pct': 26, 'high': 27, 'medium': 28},  # Y, Z, AA, AB
    '医疗机构': {'total': 29, 'risk_pct': 30, 'high': 31, 'medium': 32},  # AC, AD, AE, AF
    '福利机构': {'total': 33, 'risk_pct': 34, 'high': 35, 'medium': 36},  # AG, AH, AI, AJ
    '建筑工地': {'total': 37, 'risk_pct': 38, 'high': 39, 'medium': 40},  # AK, AL, AM, AN
    '其他场所': {'total': 41, 'risk_pct': 42, 'high': 43, 'medium': 44}   # AO, AP, AQ, AR
}
```

### 11.3 数据验证算法

#### 11.3.1 计算验证
```python
def validate_calculations(ws, row):
    """验证一览表中的计算是否正确"""
    # 读取数据
    total_communities = ws.cell(row=row, column=5).value  # E列
    risk_pct_str = ws.cell(row=row, column=6).value       # F列
    high_risk = ws.cell(row=row, column=7).value          # G列
    medium_risk = ws.cell(row=row, column=8).value        # H列

    if not all([total_communities, risk_pct_str, high_risk is not None, medium_risk is not None]):
        return False, "数据不完整"

    # 解析百分比
    risk_pct = float(risk_pct_str.replace('%', ''))

    # 计算期望值
    medium_high_count = high_risk + medium_risk
    expected_pct = (medium_high_count / total_communities) * 100 if total_communities > 0 else 0

    # 验证
    if abs(expected_pct - risk_pct) < 0.1:
        return True, "计算正确"
    else:
        return False, f"计算错误：期望{expected_pct:.1f}%，实际{risk_pct:.1f}%"
```

#### 11.3.2 数据完整性检查
```python
def check_data_completeness(ws, town_mapping):
    """检查数据完整性"""
    missing_data = []

    for town_name, mapping in town_mapping.items():
        start_row = mapping['row_start']

        # 检查一类、二类、三类监测行
        for offset in [0, 1, 2]:
            check_row = start_row + offset
            if check_row == 81:  # 跳过第81行
                continue

            communities = ws.cell(row=check_row, column=5).value
            if communities is not None and communities > 0:
                # 检查必要字段
                risk_pct = ws.cell(row=check_row, column=6).value
                if not risk_pct:
                    missing_data.append(f"{town_name}第{check_row}行缺少中高风险占比")

    return missing_data
```

## 12. 常见问题解决

### 12.1 数据异常处理

#### 12.1.1 阳性数为小数
```python
# 问题：钟落潭镇阳性数为289.14
# 原因：错误地将BI指数当作阳性数
# 解决：从BI指数计算阳性数
阳性数 = round(BI指数 × 监测户数 ÷ 100)
```

#### 12.1.2 阳性率超过100%
```python
# 问题：阳性率211.05%
# 原因：阳性数计算错误
# 解决：修正阳性数计算公式
阳性率 = (阳性数 ÷ 监测户数) × 100%
```

### 12.2 镇街数据缺失
```python
# 检查汇总表中是否有该镇街的数据
if town_name not in df['镇街'].values:
    print(f"警告：汇总表中没有{town_name}的数据")

# 检查指定日期是否有数据
town_date_data = df[(df['镇街'] == town_name) & (df['日期'] == target_date)]
if len(town_date_data) == 0:
    print(f"警告：{town_name}在{target_date}没有数据")
```

### 12.3 监测类型归类错误
```python
# 检查监测类型是否正确归类
def check_monitoring_type_classification(monitoring_type):
    if '每天1测' in monitoring_type:
        expected = '一类'
    elif '三天1测' in monitoring_type:
        expected = '三类'
    else:
        expected = '二类'

    return expected
```

---

*本教程基于白云区蚊媒监测数据处理系统v1.0*
*最后更新：2025-08-01*
