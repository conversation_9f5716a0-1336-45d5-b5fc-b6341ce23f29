# 镇街表风险等级评估系统

## 功能概述

本系统为24镇街表汇总数据添加了完整的风险等级评估功能，根据国家标准对蚊媒密度进行分级评估。

## 评级标准

### 📊 密度分级标准

| 密度分级 | 颜色级别 | 蚊幼指数 (BI) | 成蚊密度 (ADI) | 风险级别 |
|----------|----------|---------------|----------------|----------|
| **0级** | 🔵 蓝色 | 0 < BI ≤ 5 | 0 < ADI ≤ 2 | 无风险 |
| **一级** | 🟡 黄色 | 5 < BI ≤ 10 | 2 < ADI ≤ 5 | 低度风险 |
| **二级** | 🟠 橙色 | 10 < BI ≤ 20 | 5 < ADI ≤ 10 | 中度风险 |
| **三级** | 🔴 红色 | BI > 20 | ADI > 10 | 高度风险 |

### 🧮 评级逻辑

1. **蚊幼密度等级**：根据户外布雷图指数（BI）确定
2. **成蚊密度等级**：根据总雌蚊密度（ADI）确定  
3. **最终密度分级**：取蚊幼和成蚊等级中的**较高者**
4. **颜色级别**：对应密度分级的颜色标识
5. **风险级别**：对应密度分级的风险描述

## 处理结果

### 📈 数据统计

- **总记录数**: 3,455行
- **总列数**: 13列（新增5个风险评估列）
- **有效评估记录**: 487行
- **中高风险记录**: 42条

### 🎯 风险分布

| 风险级别 | 记录数 | 占比 | 说明 |
|----------|--------|------|------|
| **无风险** | 220条 | 45.2% | 🔵 密度较低，无需特别关注 |
| **低度风险** | 225条 | 46.2% | 🟡 需要常规监测 |
| **中度风险** | 38条 | 7.8% | 🟠 需要加强防控措施 |
| **高度风险** | 4条 | 0.8% | 🔴 需要紧急处理 |

### 📋 输出字段

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 镇街 | 镇街名称 | 三元里街 |
| 居委 | 居委会名称 | 中医药居委 |
| 环境类型 | 监测环境 | 居民区 |
| 监测户数 | 监测户数 | 60.0 |
| 阳性数 | 阳性数 | 10.0 |
| 户外布雷图指数（BI） | BI指数 | 16.7 |
| 总雌蚊密度（雌性ADI） | ADI指数 | 6.0 |
| **蚊幼密度等级** | 🆕 BI等级 | 二级 |
| **成蚊密度等级** | 🆕 ADI等级 | 二级 |
| **密度分级** | 🆕 最终等级 | 二级 |
| **颜色级别** | 🆕 颜色标识 | 橙色 |
| **风险级别** | 🆕 风险描述 | 中度风险 |
| 日期 | 监测日期 | 2025-07-31 |

## 使用方法

### 🚀 一键处理

```bash
# 为现有汇总表添加风险等级
python3 add_risk_levels.py
```

### 📁 输出文件

- **输入**: `汇总表_20250731.xlsx`
- **输出**: `汇总表_含风险等级_20250731.xlsx`

### 🔍 高风险记录示例

| 镇街 | 居委 | 环境类型 | BI指数 | ADI指数 | 密度分级 | 颜色级别 | 风险级别 |
|------|------|----------|--------|---------|----------|----------|----------|
| 三元里街 | 中医药居委 | 居民区 | 16.7 | 6.0 | 二级 | 橙色 | 中度风险 |
| 三元里街 | 华园居委 | 其他 | 16.3 | 6.0 | 二级 | 橙色 | 中度风险 |
| 三元里街 | 机三居委 | 居民区 | 15.7 | 0.0 | 二级 | 橙色 | 中度风险 |

## 评级逻辑验证

### ✅ 评级示例

```
BI=5.0, ADI=0.0 → 0级(蓝色, 无风险)
BI=16.7, ADI=6.0 → 二级(橙色, 中度风险)  # 取较高等级
BI=7.1, ADI=0.0 → 一级(黄色, 低度风险)
BI=1.5, ADI=5.0 → 一级(黄色, 低度风险)  # ADI=5.0为一级
```

### 🎯 关键规则

1. **BI=0或ADI=0**: 该项不参与评级
2. **取较高等级**: 蚊幼和成蚊等级取最高者
3. **空值处理**: 无数据记录显示为None
4. **边界值**: 严格按照区间判断（如BI=5为0级，BI=5.1为一级）

## 应用场景

### 🎯 监测重点

1. **高度风险区域** (红色)
   - 立即启动应急响应
   - 加强灭蚊措施
   - 增加监测频次

2. **中度风险区域** (橙色)
   - 加强日常防控
   - 定期监测评估
   - 预防措施到位

3. **低度风险区域** (黄色)
   - 维持常规监测
   - 预防性措施
   - 定期评估

4. **无风险区域** (蓝色)
   - 常规监测即可
   - 保持现有措施

### 📊 数据分析

- **趋势分析**: 对比不同时期的风险等级变化
- **区域对比**: 识别高风险镇街和居委
- **环境分析**: 分析不同环境类型的风险分布
- **预警系统**: 建立基于风险等级的预警机制

## 技术特点

### ⚡ 自动化处理

- 自动识别最新汇总表文件
- 批量计算所有记录的风险等级
- 自动生成统计报告
- 智能处理空值和异常数据

### 🔧 灵活配置

- 支持自定义评级标准
- 可调整风险等级阈值
- 支持不同的输出格式
- 易于扩展新的评估指标

### 📈 质量保证

- 完整的数据验证
- 详细的统计分析
- 高风险记录突出显示
- 评级逻辑透明可追溯

## 总结

本风险等级评估系统成功为24镇街表数据添加了标准化的风险评估功能，实现了：

- ✅ **标准化评级**: 基于国家标准的4级风险分类
- ✅ **自动化处理**: 一键完成487条记录的风险评估
- ✅ **可视化标识**: 颜色级别便于快速识别风险
- ✅ **重点突出**: 自动识别42条中高风险记录
- ✅ **数据完整**: 保留原有数据，新增5个评估字段

系统为蚊媒监测和防控工作提供了科学、标准、高效的风险评估工具，有助于精准识别防控重点，优化资源配置，提升防控效果。
