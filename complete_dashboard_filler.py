#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的Dashboard填写脚本
分析所有空单元格并从明细表中统计相应数据进行填写
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime
import logging
import openpyxl
from openpyxl import load_workbook
import shutil

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompleteDashboardFiller:
    def __init__(self):
        self.dashboard_template = '/Users/<USER>/dev/wjj01/samples/dashboard.xlsx'
        
        # 镇街映射表（按dashboard中的实际顺序）
        self.town_mapping = {
            '江高镇': {'row_start': 9},
            '人和镇': {'row_start': 12},
            '太和镇': {'row_start': 15},
            '钟落潭镇': {'row_start': 18},
            '三元里街': {'row_start': 21},
            '松洲街': {'row_start': 24},
            '景泰街': {'row_start': 27},
            '黄石街': {'row_start': 30},
            '同德街': {'row_start': 33},
            '棠景街': {'row_start': 36},
            '新市街': {'row_start': 39},
            '同和街': {'row_start': 42},
            '京溪街': {'row_start': 45},
            '永平街': {'row_start': 48},
            '均禾街': {'row_start': 51},
            '嘉禾街': {'row_start': 54},
            '石井街': {'row_start': 57},
            '金沙街': {'row_start': 60},
            '云城街': {'row_start': 63},
            '鹤龙街': {'row_start': 66},
            '白云湖街': {'row_start': 69},
            '石门街': {'row_start': 72},
            '龙归街': {'row_start': 75},
            '大源街': {'row_start': 78}
        }
        
        # 环境类型映射到列位置
        self.environment_columns = {
            '物业小区': {'total': 13, 'risk_pct': 14, 'high': 15, 'medium': 16},  # M, N, O, P
            '建筑工地': {'total': 17, 'risk_pct': 18, 'high': 19, 'medium': 20},  # Q, R, S, T
            '公园景区': {'total': 21, 'risk_pct': 22, 'high': 23, 'medium': 24},  # U, V, W, X
            '学校': {'total': 25, 'risk_pct': 26, 'high': 27, 'medium': 28},      # Y, Z, AA, AB
            '福利机构': {'total': 29, 'risk_pct': 30, 'high': 31, 'medium': 32},  # AC, AD, AE, AF
            '医疗机构': {'total': 33, 'risk_pct': 34, 'high': 35, 'medium': 36},  # AG, AH, AI, AJ
            '农贸市场': {'total': 37, 'risk_pct': 38, 'high': 39, 'medium': 40},  # AK, AL, AM, AN
            '其他场所（闲置房屋或围蔽场所）': {'total': 41, 'risk_pct': 42, 'high': 43, 'medium': 44}  # AO, AP, AQ, AR
        }
    
    def find_latest_summary_file(self):
        """查找汇总表文件（优先使用指定日期的含风险等级版本）"""
        today_str = datetime.now().strftime('%Y%m%d')

        # 优先查找7月31日的含风险等级汇总表
        target_file = f"汇总表_含风险等级_20250731_{today_str}.xlsx"
        if os.path.exists(target_file):
            logger.info(f"使用指定日期的汇总表: {target_file}")
            return target_file

        # 查找其他含风险等级的汇总表
        pattern = "汇总表_含风险等级_*.xlsx"
        files = glob.glob(pattern)

        if not files:
            # 如果没有含风险等级的，查找普通汇总表
            pattern = "汇总表_*.xlsx"
            files = glob.glob(pattern)

        if not files:
            logger.error("没有找到汇总表文件")
            logger.info("提示: 请先运行 auto_data_processor.py 生成汇总表")
            return None

        # 选择最新的文件
        latest_file = max(files, key=os.path.getmtime)

        # 获取文件信息
        file_stat = os.stat(latest_file)
        mod_time = datetime.fromtimestamp(file_stat.st_mtime)

        logger.info(f"找到最新汇总表文件: {latest_file}")
        logger.info(f"文件修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        return latest_file
    
    def analyze_summary_data(self, summary_file, target_date='2025-08-01'):
        """详细分析汇总表数据（只分析指定日期的数据）"""
        try:
            df = pd.read_excel(summary_file)
            logger.info(f"读取汇总表: {summary_file}, 数据行数: {len(df)}")

            # 筛选指定日期的数据
            if '日期' in df.columns:
                original_count = len(df)
                df = df[df['日期'] == target_date]
                logger.info(f"筛选日期 {target_date} 的数据: {len(df)} 行（原始数据: {original_count} 行）")

            if len(df) == 0:
                logger.error(f"没有找到日期 {target_date} 的数据")
                # 显示可用的日期
                if '日期' in pd.read_excel(summary_file).columns:
                    available_dates = pd.read_excel(summary_file)['日期'].unique()
                    logger.info(f"可用日期: {sorted(available_dates)}")
                return None
            
            # 全区统计
            overall_stats = self.calculate_overall_stats(df)
            
            # 按镇街统计
            town_stats = self.calculate_town_stats(df)
            
            return {
                'overall': overall_stats,
                'by_town': town_stats,
                'raw_data': df
            }
            
        except Exception as e:
            logger.error(f"分析汇总表数据失败: {e}")
            return None
    
    def calculate_overall_stats(self, df):
        """计算全区统计数据"""
        stats = {}
        
        # 基本统计
        stats['total_communities'] = df['居委'].nunique() if '居委' in df.columns else 0
        stats['total_monitoring_points'] = df['监测户数'].sum() if '监测户数' in df.columns else 0
        stats['total_positive'] = df['阳性数'].sum() if '阳性数' in df.columns else 0
        
        # 风险等级统计
        if '风险级别' in df.columns:
            risk_counts = df['风险级别'].value_counts()
            stats['no_risk'] = risk_counts.get('无风险', 0)
            stats['low_risk'] = risk_counts.get('低度风险', 0)
            stats['medium_risk'] = risk_counts.get('中度风险', 0)
            stats['high_risk'] = risk_counts.get('高度风险', 0)
            
            total_risk_records = sum([stats['no_risk'], stats['low_risk'], stats['medium_risk'], stats['high_risk']])
            if total_risk_records > 0:
                stats['medium_high_risk_pct'] = ((stats['medium_risk'] + stats['high_risk']) / total_risk_records) * 100
            else:
                stats['medium_high_risk_pct'] = 0
        else:
            stats['no_risk'] = stats['low_risk'] = stats['medium_risk'] = stats['high_risk'] = 0
            stats['medium_high_risk_pct'] = 0
        
        # 按环境类型统计
        env_stats = {}
        for env_type in self.environment_columns.keys():
            env_data = df[df['环境类型'].str.contains(env_type, na=False)]
            if len(env_data) == 0:
                # 模糊匹配
                for col_env in df['环境类型'].unique():
                    if env_type in str(col_env) or str(col_env) in env_type:
                        env_data = df[df['环境类型'] == col_env]
                        break
            
            env_risk_counts = {'无风险': 0, '低度风险': 0, '中度风险': 0, '高度风险': 0}
            if len(env_data) > 0 and '风险级别' in env_data.columns:
                for risk in env_data['风险级别'].value_counts().items():
                    if risk[0] in env_risk_counts:
                        env_risk_counts[risk[0]] = risk[1]
            
            total_env_records = sum(env_risk_counts.values())
            medium_high_pct = 0
            if total_env_records > 0:
                medium_high_pct = ((env_risk_counts['中度风险'] + env_risk_counts['高度风险']) / total_env_records) * 100
            
            env_stats[env_type] = {
                'total': len(env_data),
                'monitoring_points': env_data['监测户数'].sum() if '监测户数' in env_data.columns else 0,
                'risks': env_risk_counts,
                'medium_high_pct': medium_high_pct
            }
        
        stats['environments'] = env_stats
        return stats
    
    def calculate_town_stats(self, df):
        """计算各镇街统计数据"""
        town_stats = {}
        
        for town in df['镇街'].unique():
            town_data = df[df['镇街'] == town]
            
            # 基本统计
            stats = {
                'communities': town_data['居委'].nunique(),
                'total_records': len(town_data),
                'monitoring_points': town_data['监测户数'].sum() if '监测户数' in town_data.columns else 0,
                'positive': town_data['阳性数'].sum() if '阳性数' in town_data.columns else 0
            }
            
            # 监测类型判断
            monitoring_types = []
            if '监测类型' in town_data.columns:
                types = town_data['监测类型'].unique()
                for t in types:
                    if '一类' in str(t) or '每天1测' in str(t):
                        monitoring_types.append('一类')
                    elif '三类' in str(t) or '三天1测' in str(t):
                        monitoring_types.append('三类')
                    elif '二类' in str(t):
                        monitoring_types.append('二类')
                    else:
                        # 一类和三类以外的都作为二类
                        monitoring_types.append('二类')
            
            stats['monitoring_types'] = monitoring_types
            
            # 风险等级统计
            if '风险级别' in town_data.columns:
                risk_counts = town_data['风险级别'].value_counts()
                stats['no_risk'] = risk_counts.get('无风险', 0)
                stats['low_risk'] = risk_counts.get('低度风险', 0)
                stats['medium_risk'] = risk_counts.get('中度风险', 0)
                stats['high_risk'] = risk_counts.get('高度风险', 0)
                
                total_risk_records = sum([stats['no_risk'], stats['low_risk'], stats['medium_risk'], stats['high_risk']])
                if total_risk_records > 0:
                    stats['medium_high_risk_pct'] = ((stats['medium_risk'] + stats['high_risk']) / total_risk_records) * 100
                else:
                    stats['medium_high_risk_pct'] = 0
            else:
                stats['no_risk'] = stats['low_risk'] = stats['medium_risk'] = stats['high_risk'] = 0
                stats['medium_high_risk_pct'] = 0
            
            # 按环境类型统计（按居委统计，不是按记录统计）
            env_stats = {}
            for env_type in self.environment_columns.keys():
                # 特殊处理"其他场所（闲置房屋或围蔽场所）"
                if env_type == '其他场所（闲置房屋或围蔽场所）':
                    env_data = town_data[town_data['环境类型'].str.contains('其他场所', na=False)]
                else:
                    # 精确匹配
                    env_data = town_data[town_data['环境类型'] == env_type]
                    if len(env_data) == 0:
                        # 模糊匹配
                        env_data = town_data[town_data['环境类型'].str.contains(env_type, na=False)]

                # 按居委统计该环境类型的风险分布
                community_risks = {}
                if len(env_data) > 0:
                    for community in env_data['居委'].unique():
                        community_env_data = env_data[env_data['居委'] == community]

                        # 判断该居委在此环境类型下的风险等级（取最高风险）
                        if '高度风险' in community_env_data['风险级别'].values:
                            community_risks[community] = '高度风险'
                        elif '中度风险' in community_env_data['风险级别'].values:
                            community_risks[community] = '中度风险'
                        elif '低度风险' in community_env_data['风险级别'].values:
                            community_risks[community] = '低度风险'
                        else:
                            community_risks[community] = '无风险'

                # 统计各风险等级的居委数量
                env_risk_counts = {'无风险': 0, '低度风险': 0, '中度风险': 0, '高度风险': 0}
                for risk in community_risks.values():
                    env_risk_counts[risk] += 1

                # 计算中高风险占比（基于居委数）
                total_communities = len(community_risks)
                medium_high_pct = 0
                if total_communities > 0:
                    medium_high_count = env_risk_counts['中度风险'] + env_risk_counts['高度风险']
                    medium_high_pct = (medium_high_count / total_communities) * 100

                env_stats[env_type] = {
                    'total': total_communities,
                    'monitoring_points': env_data['监测户数'].sum() if '监测户数' in env_data.columns else 0,
                    'risks': env_risk_counts,
                    'medium_high_pct': medium_high_pct
                }
            
            stats['environments'] = env_stats
            town_stats[town] = stats

        return town_stats

    def fill_complete_dashboard(self, summary_file, target_date=None):
        """完整填写dashboard数据（只使用指定日期的数据）"""
        try:
            # 确定目标日期
            if target_date is None:
                target_date = '2025-07-31'  # 默认使用7月31日（有实际数据的日期）

            logger.info(f"Dashboard将只显示 {target_date} 的数据")

            # 分析指定日期的数据
            analysis = self.analyze_summary_data(summary_file, target_date)
            if analysis is None:
                logger.error(f"无法获取日期 {target_date} 的数据")
                return False

            # 生成输出文件名
            date_str = target_date.replace('-', '')
            output_file = f"白云区蚊媒监测一览表_完整版_{date_str}.xlsx"

            # 复制模板文件
            shutil.copy2(self.dashboard_template, output_file)

            # 使用openpyxl编辑文件
            wb = load_workbook(output_file)
            ws = wb.active

            # 更新标题
            title_cell = ws['A1']
            if title_cell.value:
                title_cell.value = f"白云区登革热和基孔肯雅热媒介伊蚊应急监测一览表（{target_date}）"

            # 填写全区数据
            self.fill_overall_data(ws, analysis['overall'])

            # 填写各镇街数据
            self.fill_town_data(ws, analysis['by_town'])

            # 保存文件
            wb.save(output_file)
            wb.close()

            # 生成填写报告
            self.generate_fill_report(analysis, output_file, target_date)

            logger.info(f"完整Dashboard填写完成: {output_file}")
            return True

        except Exception as e:
            logger.error(f"填写Dashboard失败: {e}")
            return False

    def fill_overall_data(self, ws, overall_stats):
        """填写全区数据（第6-8行）"""
        # 全区一类监测数据（第6行）
        ws.cell(row=6, column=5, value=overall_stats['total_communities'])  # E6: 监测村居数
        ws.cell(row=6, column=6, value=f"{overall_stats['medium_high_risk_pct']:.1f}%")  # F6: 中高风险占比
        ws.cell(row=6, column=7, value=overall_stats['high_risk'])  # G6: 高风险
        ws.cell(row=6, column=8, value=overall_stats['medium_risk'])  # H6: 中风险
        ws.cell(row=6, column=9, value=overall_stats['low_risk'])  # I6: 低风险
        ws.cell(row=6, column=10, value=overall_stats['no_risk'])  # J6: 风险可控

        # 监测场所数（所有环境类型的总数）
        total_sites = sum([env['total'] for env in overall_stats['environments'].values()])
        ws.cell(row=6, column=11, value=total_sites)  # K6: 监测场所数

        # 场所中高风险占比
        total_medium_high = sum([env['risks']['中度风险'] + env['risks']['高度风险'] for env in overall_stats['environments'].values()])
        total_site_records = sum([sum(env['risks'].values()) for env in overall_stats['environments'].values()])
        if total_site_records > 0:
            site_risk_pct = (total_medium_high / total_site_records) * 100
            ws.cell(row=6, column=12, value=f"{site_risk_pct:.1f}%")  # L6: 场所中高风险占比

        # 填写各环境类型的全区数据
        for env_type, columns in self.environment_columns.items():
            if env_type in overall_stats['environments']:
                env_data = overall_stats['environments'][env_type]

                # 总数
                ws.cell(row=6, column=columns['total'], value=env_data['total'])

                # 中高风险占比
                if env_data['medium_high_pct'] > 0:
                    ws.cell(row=6, column=columns['risk_pct'], value=f"{env_data['medium_high_pct']:.1f}%")

                # 高风险数
                ws.cell(row=6, column=columns['high'], value=env_data['risks']['高度风险'])

                # 中风险数
                ws.cell(row=6, column=columns['medium'], value=env_data['risks']['中度风险'])

        logger.info(f"全区数据填写完成: {overall_stats['total_communities']}个居委, {overall_stats['medium_high_risk_pct']:.1f}%中高风险占比")

    def fill_town_data(self, ws, town_stats):
        """填写各镇街数据"""
        filled_count = 0

        for town_name, town_data in town_stats.items():
            if town_name in self.town_mapping:
                mapping = self.town_mapping[town_name]
                start_row = mapping['row_start']

                # 确定主要监测类型和对应行
                main_type = '三类'  # 默认
                if town_data['monitoring_types']:
                    if '一类' in town_data['monitoring_types']:
                        main_type = '一类'
                    elif '二类' in town_data['monitoring_types']:
                        main_type = '二类'

                # 根据监测类型确定填写的行
                if main_type == '一类':
                    fill_row = start_row
                elif main_type == '二类':
                    fill_row = start_row + 1
                else:  # 三类
                    fill_row = start_row + 2

                # 跳过第81行（不需要填写数据）
                if fill_row == 81:
                    logger.info(f"跳过第81行（{town_name}），不需要填写数据")
                    continue

                # 填写基本数据
                ws.cell(row=fill_row, column=5, value=town_data['communities'])  # E: 监测村居数
                ws.cell(row=fill_row, column=6, value=f"{town_data['medium_high_risk_pct']:.1f}%")  # F: 中高风险占比
                ws.cell(row=fill_row, column=7, value=town_data['high_risk'])  # G: 高风险
                ws.cell(row=fill_row, column=8, value=town_data['medium_risk'])  # H: 中风险
                ws.cell(row=fill_row, column=9, value=town_data['low_risk'])  # I: 低风险
                ws.cell(row=fill_row, column=10, value=town_data['no_risk'])  # J: 风险可控

                # 监测场所数
                total_sites = sum([env['total'] for env in town_data['environments'].values()])
                ws.cell(row=fill_row, column=11, value=total_sites)  # K: 监测场所数

                # 场所中高风险占比
                total_medium_high = sum([env['risks']['中度风险'] + env['risks']['高度风险'] for env in town_data['environments'].values()])
                total_site_records = sum([sum(env['risks'].values()) for env in town_data['environments'].values()])
                if total_site_records > 0:
                    site_risk_pct = (total_medium_high / total_site_records) * 100
                    ws.cell(row=fill_row, column=12, value=f"{site_risk_pct:.1f}%")  # L: 场所中高风险占比

                # 填写各环境类型数据
                for env_type, columns in self.environment_columns.items():
                    if env_type in town_data['environments']:
                        env_data = town_data['environments'][env_type]

                        # 总数
                        ws.cell(row=fill_row, column=columns['total'], value=env_data['total'])

                        # 中高风险占比
                        if env_data['medium_high_pct'] > 0:
                            ws.cell(row=fill_row, column=columns['risk_pct'], value=f"{env_data['medium_high_pct']:.1f}%")

                        # 高风险数
                        ws.cell(row=fill_row, column=columns['high'], value=env_data['risks']['高度风险'])

                        # 中风险数
                        ws.cell(row=fill_row, column=columns['medium'], value=env_data['risks']['中度风险'])

                filled_count += 1
                logger.info(f"填写 {town_name} 数据: {town_data['communities']}个居委, {town_data['medium_high_risk_pct']:.1f}%中高风险占比")

        logger.info(f"共填写 {filled_count} 个镇街数据")

    def generate_fill_report(self, analysis, output_file, target_date):
        """生成填写报告"""
        try:
            report_file = f"Dashboard填写报告_{target_date.replace('-', '')}.txt"

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"白云区蚊媒监测一览表填写报告\n")
                f.write(f"填写日期: {target_date}\n")
                f.write(f"输出文件: {output_file}\n")
                f.write("=" * 60 + "\n\n")

                # 全区数据摘要
                overall = analysis['overall']
                f.write("一、全区数据填写情况\n")
                f.write("-" * 30 + "\n")
                f.write(f"监测村居数: {overall['total_communities']} 个\n")
                f.write(f"中高风险占比: {overall['medium_high_risk_pct']:.1f}%\n")
                f.write(f"高风险: {overall['high_risk']} 个\n")
                f.write(f"中风险: {overall['medium_risk']} 个\n")
                f.write(f"低风险: {overall['low_risk']} 个\n")
                f.write(f"风险可控: {overall['no_risk']} 个\n\n")

                # 镇街数据摘要
                f.write("二、镇街数据填写情况\n")
                f.write("-" * 30 + "\n")
                f.write(f"{'镇街':<12} {'居委数':<8} {'中高风险占比':<12} {'高风险':<8} {'中风险':<8} {'监测场所':<10}\n")
                f.write("-" * 70 + "\n")

                town_stats = analysis['by_town']
                for town_name, town_data in sorted(town_stats.items()):
                    total_sites = sum([env['total'] for env in town_data['environments'].values()])
                    f.write(f"{town_name:<12} {town_data['communities']:<8} "
                           f"{town_data['medium_high_risk_pct']:<12.1f}% {town_data['high_risk']:<8} "
                           f"{town_data['medium_risk']:<8} {total_sites:<10}\n")

                # 环境类型统计
                f.write("\n三、环境类型填写情况\n")
                f.write("-" * 30 + "\n")
                f.write(f"{'环境类型':<20} {'总数':<8} {'中高风险占比':<12} {'高风险':<8} {'中风险':<8}\n")
                f.write("-" * 60 + "\n")

                for env_type, env_data in overall['environments'].items():
                    if env_data['total'] > 0:
                        f.write(f"{env_type:<20} {env_data['total']:<8} "
                               f"{env_data['medium_high_pct']:<12.1f}% {env_data['risks']['高度风险']:<8} "
                               f"{env_data['risks']['中度风险']:<8}\n")

                f.write(f"\n填写完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

            logger.info(f"填写报告已生成: {report_file}")

        except Exception as e:
            logger.error(f"生成填写报告失败: {e}")

def main():
    """主函数"""
    filler = CompleteDashboardFiller()

    print("完整Dashboard填写脚本")
    print("=" * 40)

    # 查找最新的汇总表文件
    summary_file = filler.find_latest_summary_file()
    if summary_file is None:
        print("错误: 没有找到汇总表文件")
        return

    print(f"输入文件: {summary_file}")

    # 填写dashboard
    success = filler.fill_complete_dashboard(summary_file)

    if success:
        print(f"\n完整Dashboard填写成功！")
        print(f"输出文件: 白云区蚊媒监测一览表_完整版_20250731.xlsx")
        print(f"填写报告: Dashboard填写报告_20250731.txt")
    else:
        print("\nDashboard填写失败，请检查错误信息")

if __name__ == "__main__":
    main()
