#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为汇总表添加密度分级、颜色级别、风险级别字段
根据蚊幼指数(BI)和成蚊密度(ADI)进行评级
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def get_mosquito_larva_level(bi_value):
    """
    根据布雷图指数(BI)确定蚊幼密度等级
    """
    if pd.isna(bi_value) or bi_value <= 0:
        return None
    elif 0 < bi_value <= 5:
        return "0级"
    elif 5 < bi_value <= 10:
        return "一级"
    elif 10 < bi_value <= 20:
        return "二级"
    else:  # bi_value > 20
        return "三级"

def get_adult_mosquito_level(adi_value):
    """
    根据成蚊密度(ADI)确定成蚊密度等级
    """
    if pd.isna(adi_value) or adi_value <= 0:
        return None
    elif 0 < adi_value <= 2:
        return "0级"
    elif 2 < adi_value <= 5:
        return "一级"
    elif 5 < adi_value <= 10:
        return "二级"
    else:  # adi_value > 10
        return "三级"

def get_final_risk_level(larva_level, adult_level):
    """
    根据蚊幼和成蚊等级确定最终风险等级
    取两者中的较高等级
    """
    # 等级映射
    level_map = {"0级": 0, "一级": 1, "二级": 2, "三级": 3}
    reverse_map = {0: "0级", 1: "一级", 2: "二级", 3: "三级"}
    
    # 如果都为空，返回None
    if larva_level is None and adult_level is None:
        return None, None, None
    
    # 获取数值等级
    larva_num = level_map.get(larva_level, 0) if larva_level else 0
    adult_num = level_map.get(adult_level, 0) if adult_level else 0
    
    # 取较高等级
    final_level_num = max(larva_num, adult_num)
    final_level = reverse_map[final_level_num]
    
    # 确定颜色和风险
    color_map = {
        "0级": "蓝色",
        "一级": "黄色", 
        "二级": "橙色",
        "三级": "红色"
    }
    
    risk_map = {
        "0级": "无风险",
        "一级": "低度风险",
        "二级": "中度风险", 
        "三级": "高度风险"
    }
    
    return final_level, color_map[final_level], risk_map[final_level]

def add_risk_levels_to_summary(input_file, output_file=None):
    """
    为汇总表添加风险等级字段
    """
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在: {input_file}")
        return False
    
    try:
        # 读取汇总表
        df = pd.read_excel(input_file)
        print(f"读取汇总表: {input_file}")
        print(f"原始数据行数: {len(df)}")
        print(f"原始列数: {len(df.columns)}")
        
        # 检查必要的列是否存在
        required_cols = ['户外布雷图指数（BI）', '总雌蚊密度（雌性ADI）']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"错误: 缺少必要的列: {missing_cols}")
            return False
        
        print("\n开始计算风险等级...")
        
        # 计算蚊幼密度等级
        df['蚊幼密度等级'] = df['户外布雷图指数（BI）'].apply(get_mosquito_larva_level)
        
        # 计算成蚊密度等级
        df['成蚊密度等级'] = df['总雌蚊密度（雌性ADI）'].apply(get_adult_mosquito_level)
        
        # 计算最终风险等级
        risk_results = df.apply(lambda row: get_final_risk_level(
            row['蚊幼密度等级'], 
            row['成蚊密度等级']
        ), axis=1)
        
        # 分离结果
        df['密度分级'] = [result[0] for result in risk_results]
        df['颜色级别'] = [result[1] for result in risk_results]
        df['风险级别'] = [result[2] for result in risk_results]
        
        # 重新排列列顺序
        base_cols = ['镇街', '居委', '环境类型', '监测户数', '阳性数', 
                    '户外布雷图指数（BI）', '总雌蚊密度（雌性ADI）']
        
        risk_cols = ['蚊幼密度等级', '成蚊密度等级', '密度分级', '颜色级别', '风险级别']
        
        other_cols = [col for col in df.columns if col not in base_cols + risk_cols]
        
        final_cols = base_cols + risk_cols + other_cols
        existing_cols = [col for col in final_cols if col in df.columns]
        df = df[existing_cols]
        
        # 生成输出文件名
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d')
            output_file = f"汇总表_含风险等级_{timestamp}.xlsx"
        
        # 保存结果
        df.to_excel(output_file, index=False)
        print(f"\n风险等级汇总表已保存: {output_file}")
        print(f"最终数据行数: {len(df)}")
        print(f"最终列数: {len(df.columns)}")
        
        # 显示风险等级统计
        print("\n=== 风险等级统计 ===")
        
        # 有数据的记录统计
        has_data = df[(df['户外布雷图指数（BI）'] > 0) | (df['总雌蚊密度（雌性ADI）'] > 0)]
        print(f"有监测数据的记录: {len(has_data)} 行")
        
        if len(has_data) > 0:
            print("\n密度分级分布:")
            density_counts = has_data['密度分级'].value_counts().sort_index()
            for level, count in density_counts.items():
                print(f"  {level}: {count} 条记录")
            
            print("\n颜色级别分布:")
            color_counts = has_data['颜色级别'].value_counts()
            color_order = ['蓝色', '黄色', '橙色', '红色']
            for color in color_order:
                if color in color_counts:
                    print(f"  {color}: {color_counts[color]} 条记录")
            
            print("\n风险级别分布:")
            risk_counts = has_data['风险级别'].value_counts()
            risk_order = ['无风险', '低度风险', '中度风险', '高度风险']
            for risk in risk_order:
                if risk in risk_counts:
                    print(f"  {risk}: {risk_counts[risk]} 条记录")
        
        # 显示高风险记录
        high_risk = has_data[has_data['风险级别'].isin(['中度风险', '高度风险'])]
        if len(high_risk) > 0:
            print(f"\n=== 中高风险记录 ({len(high_risk)} 条) ===")
            print("前10条中高风险记录:")
            display_cols = ['镇街', '居委', '环境类型', '户外布雷图指数（BI）', 
                          '总雌蚊密度（雌性ADI）', '密度分级', '颜色级别', '风险级别']
            print(high_risk[display_cols].head(10).to_string(index=False))
        
        # 显示示例数据
        print(f"\n=== 数据示例 ===")
        print("前10行数据:")
        display_cols = ['镇街', '居委', '环境类型', '户外布雷图指数（BI）', 
                       '总雌蚊密度（雌性ADI）', '密度分级', '颜色级别', '风险级别']
        print(df[display_cols].head(10).to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"处理过程中出错: {e}")
        return False

def main():
    """
    主函数
    """
    print("汇总表风险等级添加脚本")
    print("=" * 40)
    
    # 查找汇总表文件（优先使用指定日期的汇总表）
    input_file = None
    today_str = datetime.now().strftime('%Y%m%d')

    # 优先查找7月31日的汇总表
    target_date_file = f"汇总表_20250731_{today_str}.xlsx"
    if os.path.exists(target_date_file):
        input_file = target_date_file
        print(f"使用指定日期的汇总表: {input_file}")
    else:
        # 查找其他汇总表文件
        import glob
        summary_files = glob.glob("汇总表_*.xlsx")
        if summary_files:
            # 选择最新的文件
            input_file = max(summary_files, key=os.path.getmtime)
            print(f"使用最新的汇总表: {input_file}")
        else:
            print("错误: 没有找到汇总表文件")
            print("请确保存在以下格式的文件: 汇总表_YYYYMMDD.xlsx")
            return
    
    print(f"输入文件: {input_file}")
    
    # 处理文件
    success = add_risk_levels_to_summary(input_file)
    
    if success:
        print("\n处理完成！")
        print("\n评级标准说明:")
        print("蚊幼指数(BI): 0级(0-5), 一级(5-10), 二级(10-20), 三级(>20)")
        print("成蚊密度(ADI): 0级(0-2), 一级(2-5), 二级(5-10), 三级(>10)")
        print("最终等级: 取蚊幼和成蚊等级中的较高者")
        print("颜色: 0级=蓝色, 一级=黄色, 二级=橙色, 三级=红色")
        print("风险: 0级=无风险, 一级=低度风险, 二级=中度风险, 三级=高度风险")
    else:
        print("\n处理失败，请检查错误信息")

if __name__ == "__main__":
    main()
