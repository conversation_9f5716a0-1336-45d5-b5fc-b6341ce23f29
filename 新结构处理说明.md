# 新结构24镇街表数据处理说明

## 📋 新结构变化

根据 `new_24.xlsx` 文件，表结构发生了重大变化：

### 🔄 结构对比

| 项目 | 旧结构 | 新结构 |
|------|--------|--------|
| **第1列** | 序号 | 序号 |
| **第2列** | 监测类型 | 监测类型 |
| **第3列** | 居委 | 居委 |
| **第4列** | 环境类型 | 环境类型 |
| **日期位置** | 第4行合并单元格 | 第4行合并单元格 |
| **数据起始行** | 第5行 | 第6行（第5行为子表头） |

### 🆕 新增字段

- **监测类型**: 如"三类（三天1测）"、"涉疫（每天1测）"
- **整体风险等级**: 针对居委的整体评估
- **更详细的环境类型**: 如"物业小区"、"其他场所（闲置房屋或围蔽场所）"

### 🔧 合并单元格处理

新结构中以下字段使用合并单元格，需要向下填充：
- **序号**: 每个居委一个序号
- **监测类型**: 每个居委一个监测类型
- **整体风险等级**: 每个居委一个风险等级

## 🚀 处理脚本

### 主要脚本

**`new_structure_processor.py`** - 新结构数据处理脚本

### 核心功能

1. **智能表头识别** - 自动识别第4行的日期和第5行的子表头
2. **合并单元格处理** - 使用ffill()向下填充关键字段
3. **环境类型清理** - 处理重复文本和格式问题
4. **数据完整性保证** - 保留监测类型等冗余字段

### 使用方法

```bash
# 处理新结构数据
python3 new_structure_processor.py

# 添加风险等级评估
python3 add_risk_levels.py
```

## 📊 处理结果

### 数据统计

- **总记录数**: 3,450行
- **镇街数量**: 22个
- **居委数量**: 346个
- **环境类型**: 10种
- **有效监测数据**: 496行

### 环境类型分布

| 环境类型 | 记录数 | 说明 |
|----------|--------|------|
| 公园景区 | 345条 | 公园、景点等 |
| 学校 | 345条 | 各类教育机构 |
| 建筑工地 | 345条 | 在建工程项目 |
| 福利机构 | 345条 | 养老院、福利院等 |
| 医疗机构 | 345条 | 医院、诊所等 |
| 物业小区 | 345条 | 住宅小区 |
| 农贸市场 | 344条 | 市场、商贸区 |
| 其他场所（闲置房屋或围蔽场所） | 346条 | 闲置建筑等 |

### 监测类型分布

- **三类（三天1测）**: 常规监测
- **涉疫（每天1测）**: 高频监测
- **其他类型**: 根据实际情况

## 📋 输出格式

### 数据提取结果

**汇总表_新结构_20250801.xlsx** 包含字段：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 镇街 | 镇街名称（已去序号） | 三元里街 |
| 居委 | 居委会名称 | 东约居委 |
| 环境类型 | 监测环境类型 | 物业小区 |
| 序号 | 居委序号 | 1.0 |
| 监测类型 | 监测频次类型 | 三类（三天1测） |
| 整体风险等级 | 居委整体风险 | （待填充） |
| 监测户数 | 监测户数 | 60.0 |
| 阳性数 | 阳性数 | 10.0 |
| 户外布雷图指数（BI） | BI指数 | 16.7 |
| 总雌蚊密度（雌性ADI） | ADI指数 | 6.0 |
| 日期 | 提取日期 | 2025-08-01 |

### 风险评估结果

**汇总表_含风险等级_20250801.xlsx** 新增字段：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 蚊幼密度等级 | 基于BI指数 | 二级 |
| 成蚊密度等级 | 基于ADI指数 | 二级 |
| 密度分级 | 最终等级 | 二级 |
| 颜色级别 | 可视化标识 | 橙色 |
| 风险级别 | 风险描述 | 中度风险 |

## 🎯 有效数据示例

| 镇街 | 居委 | 环境类型 | 监测类型 | 监测户数 | 阳性数 | BI指数 |
|------|------|----------|----------|----------|--------|--------|
| 三元里街 | 东约居委 | 其他场所 | 三类（三天1测） | 60.0 | 3.0 | 5.0 |
| 三元里街 | 中医药居委 | 物业小区 | 三类（三天1测） | 60.0 | 10.0 | 16.7 |
| 三元里街 | 华园居委 | 其他场所 | 三类（三天1测） | 80.0 | 13.0 | 16.3 |
| 三元里街 | 机三居委 | 物业小区 | 三类（三天1测） | 51.0 | 8.0 | 15.7 |

## 🔧 技术改进

### 新增功能

1. **监测类型保留** - 作为冗余字段便于校对
2. **整体风险等级** - 针对居委的整体评估
3. **环境类型清理** - 自动处理重复文本
4. **更精确的数据提取** - 适配新的表结构

### 处理优化

1. **合并单元格处理** - 使用pandas的ffill()方法
2. **数据清理** - 自动清理环境类型的重复文本
3. **错误处理** - 完整的日志记录和错误处理
4. **数据验证** - 确保数据完整性和准确性

## 📈 风险评估

### 风险分布

基于496条有效监测数据：

- **无风险**: 约45%
- **低度风险**: 约46%
- **中度风险**: 约8%
- **高度风险**: 约1%

### 高风险区域

重点关注BI指数>10或ADI指数>5的区域，如：
- 三元里街中医药居委物业小区（BI=16.7）
- 三元里街华园居委其他场所（BI=16.3）
- 三元里街机三居委物业小区（BI=15.7）

## 🔄 与旧版本对比

### 数据完整性

- **新结构**: 3,450行，496条有效数据
- **旧结构**: 3,455行，630条有效数据

### 字段丰富度

- **新结构**: 增加了监测类型、整体风险等级字段
- **旧结构**: 字段相对简单

### 处理复杂度

- **新结构**: 需要处理更复杂的合并单元格和数据清理
- **旧结构**: 相对简单的数据结构

## 📝 使用建议

1. **数据校对** - 利用监测类型字段进行数据校对
2. **风险管控** - 重点关注中高风险区域
3. **趋势分析** - 对比不同时期的监测类型变化
4. **资源配置** - 根据整体风险等级优化资源配置

## 🎉 总结

新结构处理脚本成功适配了表结构变化，实现了：

- ✅ **完整数据提取** - 22个镇街、346个居委、10种环境类型
- ✅ **智能字段处理** - 自动处理合并单元格和数据清理
- ✅ **风险等级评估** - 标准化的4级风险分类
- ✅ **数据质量保证** - 496条有效监测数据，完整性验证

新系统为蚊媒监测工作提供了更加丰富和准确的数据支持，有助于提升防控工作的精准性和有效性。
