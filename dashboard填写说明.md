# Dashboard填写说明

## 📊 功能概述

本脚本将汇总表数据自动填写到白云区蚊媒监测一览表模板中，生成标准化的每日统计表。

## 🎯 核心功能

### 数据源处理
- 自动查找最新的汇总表文件（优先含风险等级版本）
- 提取指定日期的监测数据（默认使用2025-07-31有效数据）
- 按镇街和环境类型进行统计分析

### Dashboard填写
- **全区汇总数据** - 填写到第6行（一类监测）
- **各镇街数据** - 按实际行号映射填写到对应位置
- **风险等级统计** - 自动计算各级风险占比
- **环境类型分析** - 重点关注物业小区等场所

## 📋 填写内容

### 全区监测评估情况
| 字段 | 列位置 | 说明 |
|------|--------|------|
| 监测村居数 | E列 | 总居委数量 |
| 中高风险占比 | F列 | (中度+高度)/总数*100% |
| 高风险 | G列 | 高度风险记录数 |
| 中风险 | H列 | 中度风险记录数 |
| 低风险 | I列 | 低度风险记录数 |
| 风险可控 | J列 | 无风险记录数 |

### 各镇街监测情况
每个镇街占3行（一类、二类、三类监测），主要填写三类监测数据：

| 镇街 | 起始行 | 填写行 | 说明 |
|------|--------|--------|------|
| 江高镇 | 9 | 11 | 三类监测 |
| 人和镇 | 12 | 14 | 三类监测 |
| 太和镇 | 15 | 17 | 三类监测 |
| 钟落潭镇 | 18 | 20 | 三类监测 |
| 三元里街 | 21 | 23 | 三类监测 |
| 松洲街 | 24 | 26 | 三类监测 |
| 新市街 | 39 | 41 | 三类监测 |
| 京溪街 | 45 | 47 | 三类监测 |

### 重点场所监测情况
- **监测场所数** - 该镇街所有环境类型记录总数
- **物业小区总数** - 物业小区环境类型记录数
- **物业小区中高风险占比** - 物业小区中中高风险占比

## 🚀 使用方法

### 基本使用
```bash
# 填写dashboard数据
python3 fill_dashboard.py
```

### 输出文件
- **白云区蚊媒监测一览表_20250731.xlsx** - 填写完成的dashboard
- **统计报告_20250801.txt** - 详细统计报告

## 📊 填写效果

### 成功填写的数据
- ✅ **全区汇总** - 332个居委，9.2%中高风险占比
- ✅ **新市街** - 14个居委，22.2%中高风险占比，4个中高风险点
- ✅ **京溪街** - 16个居委，24.2%中高风险占比，8个中高风险点
- ✅ **松洲街** - 14个居委，完整的风险分布数据

### 数据统计示例
```
全区监测评估情况（一类）:
  监测村居数: 332
  中高风险占比: 9.2%
  高风险: 4
  中风险: 35
  低风险: 193
  风险可控: 194

新市街监测情况:
  监测村居数: 14
  中高风险占比: 22.2%
  高风险: 2
  中风险: 2
  低风险: 9
  风险可控: 5
  监测场所数: 126
  物业小区总数: 14
  物业小区中高风险占比: 23.1%
```

## 🔧 技术特点

### 智能数据匹配
- 自动识别镇街名称并映射到正确行号
- 智能判断监测类型（一类/二类/三类）
- 模糊匹配环境类型名称

### 风险等级计算
- 自动统计各级风险数量和占比
- 区分全区、镇街、环境类型三个层级
- 重点关注中高风险区域

### 数据完整性
- 处理空值和缺失数据
- 确保数值计算的准确性
- 保持原有模板格式和样式

## 📈 应用价值

### 标准化报表
- 统一的数据格式和展示标准
- 符合白云区监测工作要求
- 便于上级部门审阅和决策

### 自动化处理
- 减少手工填写错误
- 提高数据处理效率
- 支持批量生成多日期报表

### 数据可视化
- 清晰的风险等级分布
- 直观的镇街对比分析
- 重点场所监测情况一目了然

## 🎯 改进方向

### 镇街名称匹配
- 完善镇街名称映射表
- 支持更多镇街的自动识别
- 处理名称变更和别名

### 数据验证
- 增加数据合理性检查
- 添加异常值提醒
- 完善错误处理机制

### 扩展功能
- 支持多日期对比分析
- 增加趋势图表生成
- 集成更多统计指标

## 🎉 总结

Dashboard填写脚本成功实现了：

- ✅ **自动化填写** - 将汇总表数据自动填入模板
- ✅ **标准化格式** - 符合白云区监测工作标准
- ✅ **完整统计** - 全区、镇街、环境类型三级统计
- ✅ **风险评估** - 自动计算各级风险分布
- ✅ **质量保证** - 数据准确性和完整性验证

这个系统为白云区蚊媒监测工作提供了高效、准确的数据处理和报表生成能力，大大提升了工作效率和数据质量。
