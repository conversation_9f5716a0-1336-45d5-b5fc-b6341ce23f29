#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成白云区"两热"媒介伊蚊应急监测一览表
基于明细数据自动填写统计表格
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

class SummaryTableGenerator:
    def __init__(self):
        # 重点场所映射（与表格列对应）
        self.key_places = {
            '物业小区': '1. 物业小区',
            '建筑工地': '2. 建筑工地', 
            '公园景区': '3. 公园景区',
            '学校': '4. 学校',
            '福利机构': '5. 福利机构',
            '医疗机构': '6. 医疗机构',
            '农贸市场': '7. 农贸市场',
            '其他场所（闲置房屋或围蔽场所）': '8. 其它场所（闲置房屋或围蔽场所）'
        }
        
        # 风险级别映射
        self.risk_levels = ['无风险', '低度风险', '中度风险', '高度风险']
    
    def load_detail_data(self, file_path):
        """
        加载明细数据
        """
        try:
            df = pd.read_excel(file_path)
            print(f"成功加载明细数据: {len(df)} 行")
            return df
        except Exception as e:
            print(f"加载明细数据失败: {e}")
            return None
    
    def calculate_village_summary(self, df, town, village):
        """
        计算单个村居的汇总数据
        """
        # 筛选该村居的数据
        village_data = df[(df['镇街'] == town) & (df['居委'] == village)]
        
        if len(village_data) == 0:
            return None
        
        # 基本统计
        total_households = village_data['监测户数'].sum()
        total_positive = village_data['阳性数'].sum()
        positive_rate = (total_positive / total_households * 100) if total_households > 0 else 0
        
        # 监测类型（取第一个非空值）
        monitoring_type = village_data['监测类型'].dropna().iloc[0] if len(village_data['监测类型'].dropna()) > 0 else ''
        if '三类' in str(monitoring_type):
            monitoring_type = '三类'
        elif '二类' in str(monitoring_type):
            monitoring_type = '二类'
        elif '一类' in str(monitoring_type):
            monitoring_type = '一类'
        
        # 风险级别统计
        risk_counts = village_data['风险级别'].value_counts()
        low_risk = risk_counts.get('低度风险', 0)
        medium_risk = risk_counts.get('中度风险', 0)
        high_risk = risk_counts.get('高度风险', 0)
        total_risk_records = low_risk + medium_risk + high_risk
        
        # 风险占比
        low_risk_pct = (low_risk / total_risk_records * 100) if total_risk_records > 0 else 0
        medium_risk_pct = (medium_risk / total_risk_records * 100) if total_risk_records > 0 else 0
        high_risk_pct = (high_risk / total_risk_records * 100) if total_risk_records > 0 else 0
        
        return {
            '镇街': town,
            '村居': village,
            '监测类型': monitoring_type,
            '监测户数': total_households,
            '低度风险个数': low_risk,
            '中度风险个数': medium_risk,
            '高度风险个数': high_risk,
            '低度风险占比': low_risk_pct,
            '中度风险占比': medium_risk_pct,
            '高度风险占比': high_risk_pct,
            '监测阳性数': total_positive,
            '监测阳性率': positive_rate
        }
    
    def calculate_place_summary(self, df, town, village, place):
        """
        计算单个村居特定场所的数据
        """
        # 筛选该村居该场所的数据
        place_data = df[(df['镇街'] == town) & (df['居委'] == village) & (df['环境类型'] == place)]
        
        if len(place_data) == 0:
            return {
                '监测户数': 0,
                '低度风险占比': 0,
                '中度风险占比': 0,
                '高度风险占比': 0
            }
        
        # 监测户数
        households = place_data['监测户数'].sum()
        
        # 风险级别统计
        risk_counts = place_data['风险级别'].value_counts()
        low_risk = risk_counts.get('低度风险', 0)
        medium_risk = risk_counts.get('中度风险', 0)
        high_risk = risk_counts.get('高度风险', 0)
        total_risk_records = low_risk + medium_risk + high_risk
        
        # 风险占比（如果只有一条记录，则该记录的风险级别占比为100%）
        if total_risk_records > 0:
            low_risk_pct = low_risk / total_risk_records * 100
            medium_risk_pct = medium_risk / total_risk_records * 100
            high_risk_pct = high_risk / total_risk_records * 100
        else:
            low_risk_pct = medium_risk_pct = high_risk_pct = 0
        
        return {
            '监测户数': households,
            '低度风险占比': low_risk_pct,
            '中度风险占比': medium_risk_pct,
            '高度风险占比': high_risk_pct
        }
    
    def generate_summary_table(self, df):
        """
        生成完整的汇总表
        """
        # 获取所有镇街和村居
        villages = df.groupby(['镇街', '居委']).size().reset_index()[['镇街', '居委']]
        
        summary_data = []
        
        # 全区汇总
        total_summary = self.calculate_village_summary(df, '', '')  # 全区数据
        if total_summary:
            total_summary['镇街'] = '全区情况'
            total_summary['村居'] = ''
            total_summary['居委会总数'] = df['居委'].nunique()
            
            # 添加重点场所数据
            for place, place_name in self.key_places.items():
                place_summary = self.calculate_place_summary(df, '', '', place)  # 全区该场所数据
                total_summary[f'{place_name}_监测户数'] = df[df['环境类型'] == place]['监测户数'].sum()
                total_summary[f'{place_name}_低度风险占比'] = place_summary['低度风险占比']
                total_summary[f'{place_name}_中度风险占比'] = place_summary['中度风险占比']
                total_summary[f'{place_name}_高度风险占比'] = place_summary['高度风险占比']
            
            summary_data.append(total_summary)
        
        # 各镇街汇总
        for town in df['镇街'].unique():
            town_data = df[df['镇街'] == town]
            town_summary = self.calculate_village_summary(town_data, town, '')
            if town_summary:
                town_summary['镇街'] = town
                town_summary['村居'] = ''
                town_summary['居委会总数'] = town_data['居委'].nunique()
                
                # 添加重点场所数据
                for place, place_name in self.key_places.items():
                    place_data = town_data[town_data['环境类型'] == place]
                    place_summary = self.calculate_place_summary(town_data, town, '', place)
                    town_summary[f'{place_name}_监测户数'] = place_data['监测户数'].sum()
                    town_summary[f'{place_name}_低度风险占比'] = place_summary['低度风险占比']
                    town_summary[f'{place_name}_中度风险占比'] = place_summary['中度风险占比']
                    town_summary[f'{place_name}_高度风险占比'] = place_summary['高度风险占比']
                
                summary_data.append(town_summary)
        
        # 各村居详细数据
        for _, row in villages.iterrows():
            town = row['镇街']
            village = row['居委']
            
            village_summary = self.calculate_village_summary(df, town, village)
            if village_summary:
                village_summary['居委会总数'] = 1  # 单个村居
                
                # 添加重点场所数据
                for place, place_name in self.key_places.items():
                    place_summary = self.calculate_place_summary(df, town, village, place)
                    village_summary[f'{place_name}_监测户数'] = place_summary['监测户数']
                    village_summary[f'{place_name}_低度风险占比'] = place_summary['低度风险占比']
                    village_summary[f'{place_name}_中度风险占比'] = place_summary['中度风险占比']
                    village_summary[f'{place_name}_高度风险占比'] = place_summary['高度风险占比']
                
                summary_data.append(village_summary)
        
        return pd.DataFrame(summary_data)
    
    def format_summary_table(self, summary_df):
        """
        格式化汇总表，使其符合标准表格格式
        """
        # 重新排列列顺序
        base_columns = [
            '镇街', '村居', '监测类型', '居委会总数', '监测户数',
            '低度风险个数', '中度风险个数', '高度风险个数',
            '低度风险占比', '中度风险占比', '高度风险占比',
            '监测阳性数', '监测阳性率'
        ]
        
        # 添加重点场所列
        place_columns = []
        for place_name in self.key_places.values():
            place_columns.extend([
                f'{place_name}_监测户数',
                f'{place_name}_低度风险占比',
                f'{place_name}_中度风险占比',
                f'{place_name}_高度风险占比'
            ])
        
        all_columns = base_columns + place_columns
        existing_columns = [col for col in all_columns if col in summary_df.columns]
        
        formatted_df = summary_df[existing_columns].copy()
        
        # 格式化数值
        for col in formatted_df.columns:
            if '占比' in col or '阳性率' in col:
                formatted_df[col] = formatted_df[col].round(2)
            elif '个数' in col or '监测户数' in col or '阳性数' in col:
                formatted_df[col] = formatted_df[col].astype(int)
        
        return formatted_df

def main():
    """
    主函数
    """
    generator = SummaryTableGenerator()
    
    # 查找最新的汇总表文件
    input_files = [
        '汇总表_含风险等级_20250801.xlsx',
        '汇总表_含风险等级_20250731.xlsx'
    ]
    
    input_file = None
    for file in input_files:
        if os.path.exists(file):
            input_file = file
            break
    
    if not input_file:
        print("错误: 没有找到包含风险等级的汇总表文件")
        print("请先运行 add_risk_levels.py 生成风险等级数据")
        return
    
    print("白云区'两热'媒介伊蚊应急监测一览表生成器")
    print("=" * 50)
    print(f"输入文件: {input_file}")
    
    # 加载明细数据
    df = generator.load_detail_data(input_file)
    if df is None:
        return
    
    print(f"数据概况:")
    print(f"  镇街数量: {df['镇街'].nunique()}")
    print(f"  居委数量: {df['居委'].nunique()}")
    print(f"  环境类型: {df['环境类型'].nunique()}")
    print(f"  有效监测数据: {len(df[df['监测户数'] > 0])} 行")
    
    # 生成汇总表
    print("\n正在生成汇总表...")
    summary_df = generator.generate_summary_table(df)
    
    # 格式化表格
    formatted_df = generator.format_summary_table(summary_df)
    
    # 保存结果
    output_file = f"两热媒介伊蚊应急监测一览表_{datetime.now().strftime('%Y%m%d')}.xlsx"
    
    try:
        formatted_df.to_excel(output_file, index=False)
        print(f"汇总表已生成: {output_file}")
        print(f"表格行数: {len(formatted_df)}")
        
        # 显示预览
        print("\n表格预览 (前5行基本信息):")
        preview_cols = ['镇街', '村居', '监测类型', '监测户数', '低度风险占比', '中度风险占比', '高度风险占比']
        print(formatted_df[preview_cols].head().to_string(index=False))
        
        print(f"\n生成完成！请查看文件: {output_file}")
        print("\n使用说明:")
        print("1. 表格包含'全区监测评估情况'和'重点场所监测情况'两部分")
        print("2. 风险占比基于明细数据中的风险级别字段计算")
        print("3. 重点场所数据按8类场所分别统计")
        print("4. 可直接用于填写正式的监测一览表")
        
    except Exception as e:
        print(f"保存文件失败: {e}")

if __name__ == "__main__":
    main()
