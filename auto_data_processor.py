#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动数据处理脚本
自动查找./data目录下以"村居监测情况"开头的最新xlsx文件进行处理
"""

import pandas as pd
import numpy as np
import os
import glob
import shutil
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AutoDataProcessor:
    def __init__(self, target_date='2025-07-31'):
        self.data_dir = './data'
        self.target_date = target_date
        self.today = datetime.now()
        self.today_str = self.today.strftime('%Y%m%d')
        self.excel_serial_today = self.get_today_excel_serial()
        
    def get_today_excel_serial(self):
        """获取今天日期对应的Excel序列号"""
        excel_epoch = datetime(1900, 1, 1)
        days_diff = (self.today - excel_epoch).days + 2
        return days_diff
    
    def find_latest_data_file(self):
        """查找data目录下以"村居监测情况"开头的最新xlsx文件"""
        try:
            if not os.path.exists(self.data_dir):
                logger.error(f"数据目录不存在: {self.data_dir}")
                return None
            
            # 查找以"村居监测情况"开头的xlsx文件
            pattern = os.path.join(self.data_dir, "村居监测情况*.xlsx")
            files = glob.glob(pattern)
            
            if not files:
                logger.error(f"在{self.data_dir}目录下没有找到以'村居监测情况'开头的xlsx文件")
                return None
            
            # 选择最新的文件（按修改时间）
            latest_file = max(files, key=os.path.getmtime)
            
            # 获取文件信息
            file_stat = os.stat(latest_file)
            mod_time = datetime.fromtimestamp(file_stat.st_mtime)
            file_size = file_stat.st_size / 1024 / 1024  # MB
            
            logger.info(f"找到最新数据文件: {latest_file}")
            logger.info(f"文件修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"文件大小: {file_size:.2f} MB")
            
            return latest_file
            
        except Exception as e:
            logger.error(f"查找数据文件失败: {e}")
            return None
    
    def backup_existing_files(self):
        """备份现有的汇总表文件到backup文件夹"""
        backup_dir = './backup'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
            logger.info(f"创建备份目录: {backup_dir}")
        
        existing_files = []
        for file in os.listdir('.'):
            if file.startswith('汇总表_') and file.endswith('.xlsx'):
                existing_files.append(file)
        
        if existing_files:
            backup_time = datetime.now().strftime('%Y%m%d_%H%M%S')
            for file in existing_files:
                backup_name = file.replace('.xlsx', f'_backup_{backup_time}.xlsx')
                backup_path = os.path.join(backup_dir, backup_name)
                shutil.move(file, backup_path)
                logger.info(f"备份文件: {file} -> {backup_path}")
        else:
            logger.info("没有找到需要备份的汇总表文件")
    
    def excel_serial_to_date(self, serial_number):
        """将Excel序列号转换为日期"""
        excel_epoch = datetime(1900, 1, 1)
        return excel_epoch + timedelta(days=int(serial_number) - 2)
    
    def find_all_date_columns(self, df_raw):
        """查找所有日期列（在多行中搜索）"""
        if len(df_raw) <= 1:
            return []

        dates = []

        # 在前5行中查找日期（扩大搜索范围，支持多种日期格式）
        for row_idx in range(min(5, len(df_raw))):
            date_row = df_raw.iloc[row_idx, :]
            for col_idx, value in enumerate(date_row):
                actual_date = None
                excel_serial = None

                # 方法1：Excel序列号格式
                if isinstance(value, (int, float)) and not pd.isna(value):
                    if 45800 <= value <= 46100:  # 2025年左右的范围
                        actual_date = self.excel_serial_to_date(value)
                        excel_serial = value

                # 方法2：datetime对象格式
                elif isinstance(value, datetime):
                    if 2025 <= value.year <= 2025:  # 2025年的数据
                        actual_date = value
                        # 转换为Excel序列号以保持兼容性
                        excel_epoch = datetime(1900, 1, 1)
                        excel_serial = (actual_date - excel_epoch).days + 2

                # 方法3：字符串日期格式
                elif isinstance(value, str) and ('2025' in value):
                    try:
                        # 尝试解析各种日期字符串格式
                        for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y年%m月%d日']:
                            try:
                                actual_date = datetime.strptime(value.split()[0], fmt)
                                if 2025 <= actual_date.year <= 2025:
                                    excel_epoch = datetime(1900, 1, 1)
                                    excel_serial = (actual_date - excel_epoch).days + 2
                                    break
                            except:
                                continue
                    except:
                        pass

                # 如果找到了有效日期，添加到列表
                if actual_date is not None and excel_serial is not None:
                    date_info = {
                        'col_idx': col_idx,
                        'row_idx': row_idx,
                        'excel_serial': excel_serial,
                        'date': actual_date,
                        'date_str': actual_date.strftime('%Y-%m-%d')
                    }

                    # 避免重复添加相同的日期
                    if not any(d['date_str'] == date_info['date_str'] and d['col_idx'] == col_idx for d in dates):
                        dates.append(date_info)

        logger.info(f"找到 {len(dates)} 个日期列")
        for date_info in dates:
            logger.info(f"第{date_info['row_idx']+1}行第{date_info['col_idx']+1}列: {date_info['excel_serial']} -> {date_info['date_str']}")

        return dates
    
    def extract_monitoring_data_for_date(self, df, date_info, sheet_name=''):
        """提取指定日期的监测数据（处理不同sheet的列结构差异）"""
        date_col_idx = date_info['col_idx']
        monitoring_cols = {}

        # 钟落潭镇特殊处理
        if '钟落潭' in sheet_name:
            logger.info("钟落潭镇使用特殊列映射")
            # 钟落潭镇的列结构：
            # 日期列+1: 监测户数（标题可能是"阳性数"）
            # 日期列+2: BI指数（标题是"户外布雷图指数（BI）"）
            # 日期列+3: ADI指数（标题是"总雌蚊密度（雌性ADI）"）
            # 日期列+4: 风险等级
            if date_col_idx + 1 < len(df.columns):
                monitoring_cols['监测户数'] = df.columns[date_col_idx + 1]
            if date_col_idx + 2 < len(df.columns):
                monitoring_cols['户外布雷图指数（BI）'] = df.columns[date_col_idx + 2]
            if date_col_idx + 3 < len(df.columns):
                monitoring_cols['总雌蚊密度（雌性ADI）'] = df.columns[date_col_idx + 3]
            if date_col_idx + 4 < len(df.columns):
                monitoring_cols['风险等级'] = df.columns[date_col_idx + 4]

            logger.info(f"钟落潭镇列映射: {list(monitoring_cols.keys())}")
            return monitoring_cols

        # 其他镇街的标准处理
        # 检查表头来确定正确的列映射
        header_row_idx = 4  # 通常表头在第5行（索引4）
        if header_row_idx < len(df):
            # 读取表头信息
            headers = []
            for col_idx in range(date_col_idx, min(date_col_idx + 8, len(df.columns))):
                if col_idx < len(df.columns):
                    header_value = df.iloc[header_row_idx, col_idx]
                    headers.append((col_idx, str(header_value) if pd.notna(header_value) else ''))

            # 根据表头内容确定列映射
            for col_idx, header_text in headers:
                if '监测户数' in header_text or '户数' in header_text:
                    monitoring_cols['监测户数'] = df.columns[col_idx]
                elif '阳性数' in header_text:
                    monitoring_cols['阳性数'] = df.columns[col_idx]
                elif 'BI' in header_text or '布雷图' in header_text:
                    monitoring_cols['户外布雷图指数（BI）'] = df.columns[col_idx]
                elif 'ADI' in header_text or '雌蚊密度' in header_text:
                    monitoring_cols['总雌蚊密度（雌性ADI）'] = df.columns[col_idx]
                elif '风险' in header_text:
                    monitoring_cols['风险等级'] = df.columns[col_idx]

        # 如果表头识别失败，使用默认的列位置（向后兼容）
        if not monitoring_cols:
            logger.warning(f"无法从表头识别列结构，使用默认列位置")
            if date_col_idx + 2 < len(df.columns):  # 监测户数（跳过地址和人员）
                monitoring_cols['监测户数'] = df.columns[date_col_idx + 2]
            if date_col_idx + 3 < len(df.columns):  # 阳性数
                monitoring_cols['阳性数'] = df.columns[date_col_idx + 3]
            if date_col_idx + 4 < len(df.columns):  # 户外布雷图指数
                monitoring_cols['户外布雷图指数（BI）'] = df.columns[date_col_idx + 4]
            if date_col_idx + 5 < len(df.columns):  # 总雌蚊密度
                monitoring_cols['总雌蚊密度（雌性ADI）'] = df.columns[date_col_idx + 5]

        logger.info(f"识别到的监测数据列: {list(monitoring_cols.keys())}")
        return monitoring_cols

    def fix_zhongluotan_data(self, df, sheet_name):
        """修正钟落潭镇的数据问题"""
        if '钟落潭' not in sheet_name:
            return df

        logger.info("检测到钟落潭镇，应用数据修正...")

        # 钟落潭镇的特殊情况：
        # - 使用特殊的列映射，已经正确提取了监测户数和BI指数
        # - 钟落潭镇没有阳性数列，需要从BI指数计算
        # - BI指数已经正确提取，无需修正

        df_fixed = df.copy()

        # 如果没有阳性数列，从BI指数计算
        if '阳性数' not in df_fixed.columns and '监测户数' in df_fixed.columns and '户外布雷图指数（BI）' in df_fixed.columns:
            # 从BI指数计算阳性数（BI = 阳性容器数 / 监测户数 * 100）
            # 阳性数 = BI * 监测户数 / 100
            df_fixed['阳性数'] = 0  # 默认为0

            mask = (df_fixed['监测户数'] > 0) & (df_fixed['户外布雷图指数（BI）'] > 0)
            if mask.any():
                # 计算阳性数并四舍五入为整数
                calculated_positive = (df_fixed.loc[mask, '户外布雷图指数（BI）'] *
                                     df_fixed.loc[mask, '监测户数'] / 100).round().astype(int)
                df_fixed.loc[mask, '阳性数'] = calculated_positive

            logger.info(f"钟落潭镇从BI指数计算阳性数，处理了 {mask.sum()} 行数据")

        return df_fixed
    
    def process_sheet(self, file_path, sheet_name):
        """处理单个sheet，提取所有日期的数据"""
        try:
            logger.info(f"正在处理sheet: {sheet_name}")
            
            # 读取原始数据分析日期列
            df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None, nrows=20)
            
            # 查找所有日期列
            date_columns = self.find_all_date_columns(df_raw)
            if not date_columns:
                logger.error(f"Sheet '{sheet_name}' 中找不到日期列")
                return None
            
            # 根据日期位置判断数据起始行
            # 如果日期在第2行，数据从第4行开始；如果日期在第4行，数据从第6行开始
            data_start_row = 3  # 默认从第4行开始（索引3）
            if date_columns and date_columns[0]['row_idx'] == 1:  # 日期在第2行
                data_start_row = 3  # 从第4行开始（索引3）
            elif date_columns and date_columns[0]['row_idx'] == 3:  # 日期在第4行
                data_start_row = 5  # 从第6行开始（索引5）

            # 读取完整数据
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=None, skiprows=data_start_row)
            
            # 设置列名
            df.columns = ['序号', '监测类型', '居委', '环境类型'] + [f'col_{i}' for i in range(4, len(df.columns))]
            
            # 处理合并单元格 - 向下填充关键字段
            fill_columns = ['序号', '监测类型', '居委']
            for col in fill_columns:
                if col in df.columns:
                    df[col] = df[col].ffill()
            
            # 清理数据
            df = df.dropna(subset=['环境类型'])  # 环境类型不能为空
            
            # 清理环境类型字段
            df['环境类型'] = df['环境类型'].astype(str).str.replace(r'^\d+\.', '', regex=True).str.strip()
            df['环境类型'] = df['环境类型'].str.replace(r'其他场所（闲置房屋或围蔽场所）.*', '其他场所（闲置房屋或围蔽场所）', regex=True)
            
            # 清理居委名称
            df['居委'] = df['居委'].astype(str).str.replace('\n', '').str.strip()
            
            # 添加镇街信息，去掉序号前缀
            clean_town_name = sheet_name
            if '.' in sheet_name and sheet_name[0].isdigit():
                clean_town_name = sheet_name.split('.', 1)[1]
            
            # 为每个日期生成数据
            all_date_data = []
            
            for date_info in date_columns:
                # 提取该日期的监测数据列
                monitoring_cols = self.extract_monitoring_data_for_date(df, date_info, sheet_name)
                
                if not monitoring_cols:
                    logger.warning(f"Sheet '{sheet_name}' 日期 {date_info['date_str']} 找不到监测数据列")
                    continue
                
                # 提取基本信息和该日期的监测数据
                basic_cols = ['序号', '监测类型', '居委', '环境类型']
                extract_cols = basic_cols + list(monitoring_cols.values())
                
                try:
                    date_data = df[extract_cols].copy()
                    
                    # 重命名监测数据列
                    rename_dict = {v: k for k, v in monitoring_cols.items()}
                    date_data = date_data.rename(columns=rename_dict)
                    
                    # 添加日期和镇街信息
                    date_data['镇街'] = clean_town_name
                    date_data['日期'] = date_info['date_str']
                    
                    # 确保数值列为数值类型
                    numeric_cols = ['监测户数', '阳性数', '户外布雷图指数（BI）', '总雌蚊密度（雌性ADI）']
                    for col in numeric_cols:
                        if col in date_data.columns:
                            date_data[col] = pd.to_numeric(date_data[col], errors='coerce').fillna(0)

                    # 修正钟落潭镇的数据问题
                    date_data = self.fix_zhongluotan_data(date_data, sheet_name)

                    # 只保留指定日期的数据
                    if date_info['date_str'] == self.target_date:
                        # 添加整体风险等级字段
                        date_data['整体风险等级'] = ''

                        all_date_data.append(date_data)
                        logger.info(f"  日期 {date_info['date_str']}: 提取 {len(date_data)} 行数据（目标日期）")
                    else:
                        logger.info(f"  日期 {date_info['date_str']}: 跳过（不是目标日期 {self.target_date}）")
                    
                except Exception as e:
                    logger.error(f"处理日期 {date_info['date_str']} 时出错: {e}")
                    continue
            
            if all_date_data:
                # 合并所有日期的数据
                sheet_all_data = pd.concat(all_date_data, ignore_index=True)
                logger.info(f"Sheet '{sheet_name}' 处理完成，总计提取 {len(sheet_all_data)} 行数据")
                return sheet_all_data
            else:
                logger.error(f"Sheet '{sheet_name}' 没有提取到任何数据")
                return None
            
        except Exception as e:
            logger.error(f"处理sheet '{sheet_name}' 失败: {e}")
            return None
    
    def process_all_sheets(self, file_path):
        """处理所有sheet"""
        try:
            xl_file = pd.ExcelFile(file_path)
            sheet_names = xl_file.sheet_names
            
            # 过滤掉汇总sheet和其他非镇街sheet
            town_sheets = [name for name in sheet_names if not name.startswith('汇总') and not name.startswith('Wps')]
            
            logger.info(f"发现 {len(town_sheets)} 个镇街sheet")
            
            all_data = []
            successful_sheets = 0
            failed_sheets = []
            
            for i, sheet_name in enumerate(town_sheets, 1):
                print(f"[{i:2d}/{len(town_sheets)}] 处理sheet: {sheet_name}")
                
                sheet_data = self.process_sheet(file_path, sheet_name)
                if sheet_data is not None:
                    all_data.append(sheet_data)
                    successful_sheets += 1
                    
                    # 统计有效数据
                    valid_data = sheet_data[sheet_data['监测户数'] > 0] if '监测户数' in sheet_data.columns else pd.DataFrame()
                    dates_count = sheet_data['日期'].nunique()
                    print(f"    成功提取 {len(sheet_data)} 行数据（{dates_count}个日期），其中有效监测数据: {len(valid_data)} 行")
                else:
                    failed_sheets.append(sheet_name)
                    print(f"    处理失败")
            
            print(f"\n处理完成: 成功 {successful_sheets} 个，失败 {len(failed_sheets)} 个")
            if failed_sheets:
                print("失败的sheet:")
                for sheet in failed_sheets:
                    print(f"  - {sheet}")
            
            return all_data
            
        except Exception as e:
            logger.error(f"处理文件失败: {e}")
            return []

def main(target_date='2025-07-31'):
    """主函数"""
    processor = AutoDataProcessor(target_date=target_date)
    
    print("自动数据处理脚本")
    print("=" * 50)
    print("自动查找./data目录下以'村居监测情况'开头的最新xlsx文件")
    print()
    
    # 查找最新的数据文件
    input_file = processor.find_latest_data_file()
    if input_file is None:
        print("错误: 没有找到数据文件")
        return
    
    print(f"输入文件: {input_file}")
    print("提取策略: 遍历所有日期，每个日期每个镇街每个居委每个环境类型一条记录")
    print()
    
    # 备份现有文件
    processor.backup_existing_files()
    
    # 处理所有sheet
    all_data = processor.process_all_sheets(input_file)
    
    if not all_data:
        print("错误: 没有提取到任何数据")
        return
    
    # 合并所有数据
    print("\n正在合并所有数据...")
    combined_df = pd.concat(all_data, ignore_index=True)
    
    # 生成输出文件名（只包含指定日期的数据）
    target_date_str = processor.target_date.replace('-', '')
    output_file = f"汇总表_{target_date_str}_{processor.today_str}.xlsx"
    
    # 保存结果
    try:
        combined_df.to_excel(output_file, index=False)
        print(f"汇总表已保存: {output_file}")
        print(f"总数据行数: {len(combined_df)}")
        
        # 显示统计信息
        print("\n数据统计:")
        print(f"  镇街数量: {combined_df['镇街'].nunique()}")
        print(f"  居委数量: {combined_df['居委'].nunique()}")
        print(f"  环境类型数量: {combined_df['环境类型'].nunique()}")
        print(f"  日期数量: {combined_df['日期'].nunique()}")
        
        # 日期分布
        print("\n日期分布:")
        date_counts = combined_df['日期'].value_counts().sort_index()
        for date, count in date_counts.items():
            print(f"  {date}: {count} 条记录")
        
        # 有效数据统计
        if '监测户数' in combined_df.columns:
            valid_data = combined_df[combined_df['监测户数'] > 0]
            print(f"\n有效监测数据: {len(valid_data)} 行")
            
            # 按日期统计有效数据
            print("各日期有效数据分布:")
            valid_by_date = valid_data['日期'].value_counts().sort_index()
            for date, count in valid_by_date.items():
                print(f"  {date}: {count} 条有效记录")
        
        # 显示前几行数据
        print("\n汇总表预览 (前10行):")
        display_cols = ['镇街', '居委', '环境类型', '日期', '监测户数', '阳性数']
        print(combined_df[display_cols].head(10).to_string(index=False))
        
        print(f"\n处理完成！数据已保存到: {output_file}")
        
    except Exception as e:
        print(f"保存文件失败: {e}")

if __name__ == "__main__":
    main()
