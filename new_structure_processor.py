#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新表结构的24镇街表数据处理脚本
适配新的表结构：序号、监测类型、居委、环境类型
"""

import pandas as pd
import numpy as np
import os
import shutil
import openpyxl
from openpyxl import load_workbook
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NewStructureProcessor:
    def __init__(self):
        self.today = datetime.now()
        self.today_str = self.today.strftime('%Y%m%d')
        self.excel_serial_today = self.get_today_excel_serial()
        
    def get_today_excel_serial(self):
        """
        获取今天日期对应的Excel序列号
        """
        excel_epoch = datetime(1900, 1, 1)
        days_diff = (self.today - excel_epoch).days + 2
        return days_diff
    
    def backup_existing_files(self):
        """
        备份现有的汇总表文件到backup文件夹
        """
        backup_dir = './backup'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
            logger.info(f"创建备份目录: {backup_dir}")
        
        existing_files = []
        for file in os.listdir('.'):
            if file.startswith('汇总表_') and file.endswith('.xlsx'):
                existing_files.append(file)
        
        if existing_files:
            backup_time = datetime.now().strftime('%Y%m%d_%H%M%S')
            for file in existing_files:
                backup_name = file.replace('.xlsx', f'_backup_{backup_time}.xlsx')
                backup_path = os.path.join(backup_dir, backup_name)
                shutil.move(file, backup_path)
                logger.info(f"备份文件: {file} -> {backup_path}")
        else:
            logger.info("没有找到需要备份的汇总表文件")
    
    def find_date_column(self, df_raw, target_date):
        """
        在第4行（索引3）中查找目标日期对应的列
        """
        if len(df_raw) <= 3:
            return None
            
        # 在第4行查找日期
        row_data = df_raw.iloc[3, :]
        for col_idx, value in enumerate(row_data):
            if isinstance(value, (int, float)) and not pd.isna(value):
                if abs(value - target_date) <= 1:  # 允许1天的误差
                    logger.info(f"找到日期列 {col_idx}: {value} (目标: {target_date})")
                    return col_idx
        
        # 如果没有找到精确匹配，查找最接近的
        date_candidates = []
        for col_idx, value in enumerate(row_data):
            if isinstance(value, (int, float)) and not pd.isna(value):
                if 45800 <= value <= 46000:  # 2025年左右的范围
                    date_candidates.append((col_idx, value))
        
        if date_candidates:
            closest = min(date_candidates, key=lambda x: abs(x[1] - target_date))
            logger.warning(f"使用最接近的日期列 {closest[0]}: {closest[1]} (目标: {target_date}, 差距: {abs(closest[1] - target_date)} 天)")
            return closest[0]
        
        return None
    
    def process_sheet(self, file_path, sheet_name):
        """
        处理单个sheet，提取今天日期的数据
        """
        try:
            logger.info(f"正在处理sheet: {sheet_name}")
            
            # 直接读取数据，不处理合并单元格（使用pandas的向下填充）
            df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None, nrows=20)
            
            # 查找日期列（在第4行，索引3）
            date_col_idx = self.find_date_column(df_raw, self.excel_serial_today)
            if date_col_idx is None:
                logger.error(f"Sheet '{sheet_name}' 中找不到今天日期的列")
                return None
            
            # 读取完整数据，从第6行开始（索引5）作为数据行，跳过第5行的子表头
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=None, skiprows=5)

            # 设置列名
            df.columns = ['序号', '监测类型', '居委', '环境类型'] + [f'col_{i}' for i in range(4, len(df.columns))]

            # 处理合并单元格 - 向下填充关键字段
            fill_columns = ['序号', '监测类型', '居委']
            for col in fill_columns:
                if col in df.columns:
                    df[col] = df[col].ffill()  # 使用新的方法
            
            # 清理数据
            df = df.dropna(subset=['环境类型'])  # 环境类型不能为空
            
            # 清理环境类型字段（去掉数字前缀和重复文本）
            df['环境类型'] = df['环境类型'].astype(str).str.replace(r'^\d+\.', '', regex=True).str.strip()
            # 处理重复的环境类型文本
            df['环境类型'] = df['环境类型'].str.replace(r'其他场所（闲置房屋或围蔽场所）.*', '其他场所（闲置房屋或围蔽场所）', regex=True)
            
            # 找到今天日期列后面的监测数据列
            monitoring_cols = {}

            # 根据实际观察的数据结构：
            # 日期列（如列4是45869）后面依次是：监测具体地址、监测人员、监测户数、阳性数、BI指数、雌蚊密度
            # 在df中，这些列的索引就是date_col_idx + offset

            if date_col_idx + 2 < len(df.columns):  # 监测户数（跳过地址和人员）
                monitoring_cols['监测户数'] = df.columns[date_col_idx + 2]
            if date_col_idx + 3 < len(df.columns):  # 阳性数
                monitoring_cols['阳性数'] = df.columns[date_col_idx + 3]
            if date_col_idx + 4 < len(df.columns):  # 户外布雷图指数
                monitoring_cols['户外布雷图指数（BI）'] = df.columns[date_col_idx + 4]
            if date_col_idx + 5 < len(df.columns):  # 总雌蚊密度
                monitoring_cols['总雌蚊密度（雌性ADI）'] = df.columns[date_col_idx + 5]

            logger.info(f"监测数据列映射: {monitoring_cols}")
            
            if not monitoring_cols:
                logger.error(f"Sheet '{sheet_name}' 中找不到监测数据列")
                return None
            
            # 提取数据
            basic_cols = ['序号', '监测类型', '居委', '环境类型']
            extract_cols = basic_cols + list(monitoring_cols.values())
            sheet_data = df[extract_cols].copy()
            
            # 重命名监测数据列
            rename_dict = {v: k for k, v in monitoring_cols.items()}
            sheet_data = sheet_data.rename(columns=rename_dict)
            
            # 添加镇街信息，去掉序号前缀
            clean_town_name = sheet_name
            if '.' in sheet_name and sheet_name[0].isdigit():
                clean_town_name = sheet_name.split('.', 1)[1]
            
            sheet_data['镇街'] = clean_town_name
            sheet_data['日期'] = self.today.strftime('%Y-%m-%d')
            
            # 清理数据
            sheet_data['居委'] = sheet_data['居委'].astype(str).str.replace('\n', '').str.strip()
            sheet_data['环境类型'] = sheet_data['环境类型'].astype(str).str.replace('\n', '').str.strip()
            
            # 确保数值列为数值类型
            numeric_cols = ['监测户数', '阳性数', '户外布雷图指数（BI）', '总雌蚊密度（雌性ADI）']
            for col in numeric_cols:
                if col in sheet_data.columns:
                    sheet_data[col] = pd.to_numeric(sheet_data[col], errors='coerce').fillna(0)
            
            # 添加整体风险等级字段（从序号字段推断，这里先设为空，后续可以根据需要填充）
            sheet_data['整体风险等级'] = ''  # 可以根据实际需求填充
            
            logger.info(f"Sheet '{sheet_name}' 处理完成，提取 {len(sheet_data)} 行数据")
            return sheet_data
            
        except Exception as e:
            logger.error(f"处理sheet '{sheet_name}' 失败: {e}")
            return None
    
    def aggregate_data(self, all_data):
        """
        汇总所有数据，按镇街-居委-环境类型分组
        """
        if not all_data:
            logger.error("没有数据可以汇总")
            return None
        
        # 合并所有数据
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # 清理数据
        combined_df = combined_df.dropna(subset=['居委', '环境类型'])
        combined_df['居委'] = combined_df['居委'].astype(str).str.replace('\n', '').str.strip()
        combined_df['环境类型'] = combined_df['环境类型'].astype(str).str.replace('\n', '').str.strip()
        
        # 按镇街、居委和环境类型分组汇总
        numeric_cols = ['监测户数', '阳性数', '户外布雷图指数（BI）', '总雌蚊密度（雌性ADI）']
        
        # 确保数值列为数值类型
        for col in numeric_cols:
            if col in combined_df.columns:
                combined_df[col] = pd.to_numeric(combined_df[col], errors='coerce').fillna(0)
        
        # 分组汇总
        agg_dict = {}
        for col in numeric_cols:
            if col in combined_df.columns:
                agg_dict[col] = 'sum'
        
        if not agg_dict:
            logger.error("没有找到可汇总的数值列")
            return None
        
        # 添加其他信息字段
        agg_dict['序号'] = 'first'
        agg_dict['监测类型'] = 'first'
        agg_dict['整体风险等级'] = 'first'
        agg_dict['日期'] = 'first'
        
        summary_df = combined_df.groupby(['镇街', '居委', '环境类型']).agg(agg_dict).reset_index()
        
        # 重新排列列顺序
        final_cols = ['镇街', '居委', '环境类型', '序号', '监测类型', '整体风险等级',
                     '监测户数', '阳性数', '户外布雷图指数（BI）', '总雌蚊密度（雌性ADI）', '日期']
        
        existing_cols = [col for col in final_cols if col in summary_df.columns]
        summary_df = summary_df[existing_cols]
        
        return summary_df
    
    def process_all_sheets(self, file_path):
        """
        处理所有sheet
        """
        try:
            xl_file = pd.ExcelFile(file_path)
            sheet_names = xl_file.sheet_names
            
            # 过滤掉汇总sheet和其他非镇街sheet
            town_sheets = [name for name in sheet_names if not name.startswith('汇总') and not name.startswith('Wps')]
            
            logger.info(f"发现 {len(town_sheets)} 个镇街sheet")
            
            all_data = []
            successful_sheets = 0
            failed_sheets = []
            
            for i, sheet_name in enumerate(town_sheets, 1):
                print(f"[{i:2d}/{len(town_sheets)}] 处理sheet: {sheet_name}")
                
                sheet_data = self.process_sheet(file_path, sheet_name)
                if sheet_data is not None:
                    all_data.append(sheet_data)
                    successful_sheets += 1
                    
                    # 统计有效数据
                    valid_data = sheet_data[sheet_data['监测户数'] > 0] if '监测户数' in sheet_data.columns else pd.DataFrame()
                    print(f"    成功提取 {len(sheet_data)} 行数据，其中有效监测数据: {len(valid_data)} 行")
                else:
                    failed_sheets.append(sheet_name)
                    print(f"    处理失败")
            
            print(f"\n处理完成: 成功 {successful_sheets} 个，失败 {len(failed_sheets)} 个")
            if failed_sheets:
                print("失败的sheet:")
                for sheet in failed_sheets:
                    print(f"  - {sheet}")
            
            return all_data
            
        except Exception as e:
            logger.error(f"处理文件失败: {e}")
            return []

def main():
    """
    主函数
    """
    processor = NewStructureProcessor()
    
    # 输入文件路径
    input_file = '/Users/<USER>/dev/wjj01/samples/new_24.xlsx'
    
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在: {input_file}")
        return
    
    print("新结构24镇街表数据提取脚本")
    print("=" * 40)
    print(f"输入文件: {input_file}")
    print(f"提取日期: {processor.today.strftime('%Y年%m月%d日')}")
    print(f"Excel序列号: {processor.excel_serial_today}")
    print()
    
    # 备份现有文件
    processor.backup_existing_files()
    
    # 处理所有sheet
    all_data = processor.process_all_sheets(input_file)
    
    if not all_data:
        print("错误: 没有提取到任何数据")
        return
    
    # 汇总数据
    print("\n正在汇总数据...")
    summary_df = processor.aggregate_data(all_data)
    
    if summary_df is None:
        print("错误: 数据汇总失败")
        return
    
    # 生成输出文件名
    output_file = f"汇总表_新结构_{processor.today_str}.xlsx"
    
    # 保存结果
    try:
        summary_df.to_excel(output_file, index=False)
        print(f"汇总表已保存: {output_file}")
        print(f"总数据行数: {len(summary_df)}")
        
        # 显示统计信息
        print("\n数据统计:")
        print(f"  镇街数量: {summary_df['镇街'].nunique()}")
        print(f"  居委数量: {summary_df['居委'].nunique()}")
        print(f"  环境类型数量: {summary_df['环境类型'].nunique()}")
        
        # 有效数据统计
        if '监测户数' in summary_df.columns:
            valid_data = summary_df[summary_df['监测户数'] > 0]
            print(f"  有效监测数据: {len(valid_data)} 行")
        
        # 显示前几行数据
        print("\n汇总表预览 (前10行):")
        display_cols = ['镇街', '居委', '环境类型', '监测类型', '监测户数', '阳性数']
        print(summary_df[display_cols].head(10).to_string(index=False))
        
        print(f"\n处理完成！数据已保存到: {output_file}")
        
    except Exception as e:
        print(f"保存文件失败: {e}")

if __name__ == "__main__":
    main()
