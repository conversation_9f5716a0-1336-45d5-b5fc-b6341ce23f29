# Dashboard填充说明

## 📊 功能概述

Dashboard填充脚本能够从最新的汇总表中读取数据，并基于dashboard模板创建完整的统计表，实现数据的自动化填充和格式化。

## 🎯 核心功能

### 数据源识别

- **自动查找最新汇总表**: 支持多种汇总表格式
  - `汇总表_含风险等级_*.xlsx` - 优先级最高
  - `汇总表_所有日期_*.xlsx` - 时间序列数据
  - `汇总表_新结构_*.xlsx` - 新结构数据
  - `汇总表_*.xlsx` - 基础汇总表

### 模板结构分析

- **智能日期识别**: 自动识别dashboard模板中的日期列
- **数据列映射**: 精确映射监测数据列（监测户数、阳性数、BI指数、ADI指数）
- **合并单元格处理**: 自动取消合并单元格以便数据填充

### 动态结构创建

- **居委扩展**: 根据汇总表中的实际居委创建对应记录
- **环境类型标准化**: 支持9种标准环境类型
- **数据行生成**: 为每个居委-环境类型组合创建完整记录

## 🚀 使用方法

### 基本使用

```bash
# 运行dashboard填充脚本
python3 dashboard_filler.py
```

### 前置条件

1. **汇总表文件**: 确保当前目录有最新的汇总表文件
2. **Dashboard模板**: 确保`/Users/<USER>/dev/wjj01/samples/dashboard.xlsx`存在
3. **Python依赖**: 安装pandas和openpyxl

## 📋 处理结果

### 数据规模

基于最新处理结果：

- **总记录数**: 2,583行
- **居委数量**: 287个
- **环境类型**: 9种完整覆盖
- **有效数据**: 497条监测记录
- **数据点**: 1,988个数据点填充

### 输出文件

**dashboard_created_YYYYMMDD_HHMMSS.xlsx**

- 基于原始dashboard模板格式
- 包含所有居委的完整环境类型记录
- 填充了实际的监测数据
- 保持原有的表头和日期结构

## 🔧 技术特点

### 智能映射

1. **环境类型映射**
   ```python
   mapping = {
       '物业小区': '居民区',
       '其他场所（闲置房屋或围蔽场所）': '其他',
       '农贸市场': '商贸区',
       '闲置房屋': '其他'
   }
   ```

2. **日期列识别**
   - 自动识别Excel序列号格式的日期
   - 支持多个日期列的并行处理
   - 精确映射日期到对应的数据列

3. **数据验证**
   - 数值类型验证和转换
   - 空值处理和默认值设置
   - 数据完整性检查

### 结构处理

1. **合并单元格处理**
   - 自动取消所有合并单元格
   - 避免写入冲突和格式问题
   - 保持数据的独立性

2. **动态行创建**
   - 根据实际数据创建所需行数
   - 标准化的9种环境类型结构
   - 序号和居委信息的正确填充

## 📊 数据结构

### Dashboard格式

| 列 | 字段名 | 说明 |
|----|--------|------|
| A | 序号 | 居委编号 |
| B | 居委 | 居委会名称 |
| C | 监测点 | 监测点信息（预留） |
| D | 环境类型 | 9种标准环境类型 |
| E | 监测具体地址 | 地址信息（预留） |
| F+ | 日期数据列 | 监测人员、监测户数、阳性数、BI指数、ADI指数 |

### 环境类型标准

1. **居民区** - 住宅小区等居住区域
2. **公园景区** - 公园、景点等休闲场所
3. **医疗机构** - 医院、诊所等医疗场所
4. **学校** - 各类教育机构
5. **福利机构** - 养老院、福利院等
6. **建筑工地** - 在建工程项目
7. **闲置房屋** - 空置建筑物
8. **商贸区** - 商场、市场等商业区
9. **其他** - 其他类型场所

## 📈 处理流程

### 1. 数据准备阶段

- 查找最新汇总表文件
- 加载并验证数据完整性
- 筛选有效监测数据（监测户数>0）

### 2. 模板分析阶段

- 加载dashboard模板文件
- 分析日期列结构和数据列映射
- 识别现有的行结构和格式

### 3. 结构重建阶段

- 取消所有合并单元格
- 清空现有数据行
- 根据汇总表数据创建新的行结构

### 4. 数据填充阶段

- 创建居委-环境类型组合记录
- 填充实际监测数据
- 验证数据完整性和准确性

### 5. 输出保存阶段

- 生成带时间戳的输出文件
- 保存完整的dashboard数据
- 生成处理报告和统计信息

## 🎯 应用场景

### 日常监测报告

- 快速生成标准化的监测统计表
- 支持多个日期的数据展示
- 便于数据分析和趋势观察

### 数据汇总分析

- 将分散的监测数据整合到统一格式
- 支持按居委、环境类型的分类统计
- 便于制作图表和可视化分析

### 上报和存档

- 生成符合标准格式的报告文件
- 保持数据的完整性和可追溯性
- 支持历史数据的对比分析

## ⚠️ 注意事项

### 文件路径

- Dashboard模板路径固定为`/Users/<USER>/dev/wjj01/samples/dashboard.xlsx`
- 汇总表文件需要在当前工作目录
- 输出文件保存在当前工作目录

### 数据要求

- 汇总表必须包含`居委`、`环境类型`、`监测户数`等关键字段
- 数值字段需要是有效的数字格式
- 日期字段需要是标准的日期格式

### 性能考虑

- 大量数据处理时可能需要较长时间
- 建议在处理前备份原始文件
- 输出文件较大时注意磁盘空间

## 🔄 版本兼容性

### 支持的汇总表格式

- ✅ 基础汇总表格式
- ✅ 新结构汇总表格式  
- ✅ 遍历所有日期格式
- ✅ 含风险等级评估格式

### Dashboard模板要求

- 必须包含标准的表头结构
- 日期列必须使用Excel序列号格式
- 环境类型列必须在D列位置

## 🎉 处理效果

### 成功案例

最新处理结果显示：

- ✅ **完整覆盖**: 287个居委，9种环境类型
- ✅ **数据准确**: 497条有效监测记录，100%匹配
- ✅ **格式标准**: 1,988个数据点精确填充
- ✅ **结构完整**: 2,583行完整记录生成

### 质量保证

- **数据完整性**: 每个居委都有9种环境类型的完整记录
- **格式一致性**: 保持dashboard模板的原有格式和结构
- **数值准确性**: 所有监测数据精确对应到正确位置
- **可追溯性**: 输出文件包含时间戳，便于版本管理

这个Dashboard填充系统为蚊媒监测数据的标准化展示和分析提供了强大的自动化支持，大大提升了数据处理的效率和准确性。
