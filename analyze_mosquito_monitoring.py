#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
白云区蚊媒监测数据分析脚本
分析重点区域监测统计中的各环境类型指标
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys

def load_excel_data(file_path):
    """加载Excel文件数据"""
    try:
        # 读取汇总表
        df_summary = pd.read_excel(file_path, sheet_name='汇总', header=None)
        print(f"成功加载汇总表，数据形状: {df_summary.shape}")
        return df_summary
    except Exception as e:
        print(f"加载Excel文件失败: {e}")
        return None

def analyze_monitoring_data(df):
    """分析监测数据"""
    if df is None:
        return None

    # 找到表头行（包含"物业小区"等环境类型的行）
    header_row = None
    for idx, row in df.iterrows():
        if any('物业小区' in str(cell) for cell in row if pd.notna(cell)):
            header_row = idx
            break

    if header_row is None:
        print("未找到包含环境类型的表头行")
        return None

    print(f"找到表头行: {header_row}")

    # 打印表头信息用于调试
    print("表头行内容:")
    for i in range(max(0, header_row-1), min(df.shape[0], header_row+3)):
        print(f"  行{i}: {list(df.iloc[i, :20])}")  # 只显示前20列

    # 找到各环境类型的列位置 - 根据实际Excel结构调整
    environment_columns = {}

    # 查找8个环境类型的列位置
    environments = [
        '物业小区', '建筑工地', '公园景区', '学校',
        '福利机构', '医疗机构', '农贸市场', '其它场所'
    ]

    # 根据观察到的Excel结构，手动设置列位置
    # 从Excel数据可以看出：
    # 物业小区: 列M(12), N(13), O(14), P(15) - 总数、中高风险占比、高风险、中风险
    # 建筑工地: 列Q(16), R(17), S(18), T(19)
    # 公园景区: 列U(20), V(21), W(22), X(23)
    # 学校: 列Y(24), Z(25), AA(26), AB(27)
    # 福利机构: 列AC(28), AD(29), AE(30), AF(31)
    # 医疗机构: 列AG(32), AH(33), AI(34), AJ(35)
    # 农贸市场: 列AK(36), AL(37), AM(38), AN(39)
    # 其它场所: 列AO(40), AP(41), AQ(42), AR(43)

    environment_columns = {
        '物业小区': [12, 13, 14, 15],
        '建筑工地': [16, 17, 18, 19],
        '公园景区': [20, 21, 22, 23],
        '学校': [24, 25, 26, 27],
        '福利机构': [28, 29, 30, 31],
        '医疗机构': [32, 33, 34, 35],
        '农贸市场': [36, 37, 38, 39],
        '其它场所': [40, 41, 42, 43]
    }

    print("环境类型列位置:")
    for env, cols in environment_columns.items():
        print(f"  {env}: {cols}")

    # 分析数据行（从表头后开始）
    data_start_row = 5  # 根据Excel结构，数据从第6行开始（索引5）
    results = {}

    # 先打印一些数据行来调试
    print("\n调试信息 - 前10行数据:")
    for i in range(data_start_row, min(data_start_row + 10, df.shape[0])):
        district = df.iloc[i, 1]
        monitor_type = df.iloc[i, 3]
        print(f"行{i}: 镇街={district}, 监测类型={monitor_type}")
        # 打印各环境类型的数据
        for env, cols in environment_columns.items():
            if len(cols) >= 4:
                total = df.iloc[i, cols[0]]
                high_risk = df.iloc[i, cols[2]]
                medium_risk = df.iloc[i, cols[3]]
                print(f"  {env}: 总数={total}, 高风险={high_risk}, 中风险={medium_risk}")
        print()

    for env in environments:
        if env in environment_columns:
            cols = environment_columns[env]
            total_col = cols[0]      # 总数
            risk_ratio_col = cols[1] # 中高风险占比
            high_risk_col = cols[2]  # 高风险
            medium_risk_col = cols[3] # 中风险

            # 统计各镇街的数据
            env_stats = {
                'total_count': 0,
                'high_risk_count': 0,
                'medium_risk_count': 0,
                'districts': []
            }

            # 遍历数据行
            for row_idx in range(data_start_row, df.shape[0]):
                district = df.iloc[row_idx, 1]  # 镇街列
                monitor_type = df.iloc[row_idx, 3]  # 监测类型列

                if pd.notna(district) and pd.notna(monitor_type) and str(district) != '全区情况':
                    total = df.iloc[row_idx, total_col]
                    high_risk = df.iloc[row_idx, high_risk_col]
                    medium_risk = df.iloc[row_idx, medium_risk_col]

                    # 调试输出
                    if row_idx < data_start_row + 5:  # 只打印前5行的调试信息
                        print(f"调试 {env} - 行{row_idx}: 镇街={district}, 监测类型={monitor_type}")
                        print(f"  总数={total}, 高风险={high_risk}, 中风险={medium_risk}")

                    # 只统计有监测数据的记录（总数>0或者有具体数值）
                    if pd.notna(total) and (isinstance(total, (int, float)) and total > 0):
                        env_stats['total_count'] += 1  # 计数单位：一个镇街-环境类型组合

                        if pd.notna(high_risk) and (isinstance(high_risk, (int, float)) and high_risk > 0):
                            env_stats['high_risk_count'] += 1
                        if pd.notna(medium_risk) and (isinstance(medium_risk, (int, float)) and medium_risk > 0):
                            env_stats['medium_risk_count'] += 1

                        env_stats['districts'].append({
                            'district': district,
                            'monitor_type': monitor_type,
                            'total': total,
                            'high_risk': high_risk if pd.notna(high_risk) else 0,
                            'medium_risk': medium_risk if pd.notna(medium_risk) else 0
                        })

            # 计算中高风险占比
            medium_high_risk_count = env_stats['high_risk_count'] + env_stats['medium_risk_count']
            risk_ratio = (medium_high_risk_count / env_stats['total_count'] * 100) if env_stats['total_count'] > 0 else 0

            results[env] = {
                'total_count': env_stats['total_count'],
                'medium_high_risk_ratio': round(risk_ratio, 1),
                'high_risk_count': env_stats['high_risk_count'],
                'medium_risk_count': env_stats['medium_risk_count'],
                'districts': env_stats['districts']
            }

    return results

def print_results(results):
    """打印分析结果"""
    if not results:
        print("没有分析结果")
        return

    print("\n" + "="*100)
    print("白云区蚊媒监测重点区域监测统计分析结果")
    print("="*100)
    print("统计说明：计数单位为 日期-镇街-居委-指定环境类型 的组合")
    print("当监测户数>0时，表示当天检查了该环境，总数+1")
    print("-"*100)

    # 按照指定顺序输出所有8个环境类型
    all_envs = ['物业小区', '建筑工地', '公园景区', '学校', '福利机构', '医疗机构', '农贸市场', '其它场所']

    # 创建汇总表格
    print(f"\n{'环境类型':<12} {'总数':<8} {'中高风险占比':<12} {'高风险':<8} {'中风险':<8}")
    print("-" * 60)

    for env in all_envs:
        if env in results:
            data = results[env]
            print(f"{env:<12} {data['total_count']:<8} {data['medium_high_risk_ratio']:<12}% {data['high_risk_count']:<8} {data['medium_risk_count']:<8}")

    print("\n" + "="*100)
    print("详细分析")
    print("="*100)

    for env in all_envs:
        if env in results:
            data = results[env]
            print(f"\n【{env}】")
            print(f"  总数: {data['total_count']}")
            print(f"  中高风险占比: {data['medium_high_risk_ratio']}%")
            print(f"  高风险: {data['high_risk_count']}")
            print(f"  中风险: {data['medium_risk_count']}")

            # 显示所有详细记录
            if data['districts']:
                print(f"  详细记录:")
                for i, district_data in enumerate(data['districts']):
                    risk_status = ""
                    if district_data['high_risk'] > 0:
                        risk_status = " [高风险]"
                    elif district_data['medium_risk'] > 0:
                        risk_status = " [中风险]"
                    else:
                        risk_status = " [低风险/风险可控]"

                    print(f"    {i+1:2d}. {district_data['district']:<12} - {district_data['monitor_type']:<6} - "
                          f"总数:{district_data['total']:3d}, 高风险:{district_data['high_risk']:2d}, "
                          f"中风险:{district_data['medium_risk']:2d}{risk_status}")
            print("-" * 80)

def main():
    """主函数"""
    file_path = "/Users/<USER>/dev/wjj01/白云区蚊媒监测一览表_完整版_20250731.xlsx"
    
    if not Path(file_path).exists():
        print(f"文件不存在: {file_path}")
        return
    
    print(f"开始分析文件: {file_path}")
    
    # 加载数据
    df = load_excel_data(file_path)
    
    # 分析数据
    results = analyze_monitoring_data(df)
    
    # 输出结果
    print_results(results)

if __name__ == "__main__":
    main()
