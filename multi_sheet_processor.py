#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多Sheet镇街表数据处理脚本
从包含24个sheet的Excel文件中提取今天日期的数据
"""

import pandas as pd
import numpy as np
import os
import shutil
import openpyxl
from openpyxl import load_workbook
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MultiSheetProcessor:
    def __init__(self):
        self.today = datetime.now()
        self.today_str = self.today.strftime('%Y%m%d')
        self.excel_serial_today = self.get_today_excel_serial()
        
    def get_today_excel_serial(self):
        """
        获取今天日期对应的Excel序列号
        """
        # Excel的日期序列号从1900年1月1日开始，但有一个bug认为1900年是闰年
        excel_epoch = datetime(1900, 1, 1)
        days_diff = (self.today - excel_epoch).days + 2
        return days_diff
    
    def backup_existing_files(self):
        """
        备份现有的汇总表文件到backup文件夹
        """
        backup_dir = './backup'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
            logger.info(f"创建备份目录: {backup_dir}")
        
        # 查找现有的汇总表文件
        existing_files = []
        for file in os.listdir('.'):
            if file.startswith('汇总表_') and file.endswith('.xlsx'):
                existing_files.append(file)
        
        if existing_files:
            backup_time = datetime.now().strftime('%Y%m%d_%H%M%S')
            for file in existing_files:
                backup_name = file.replace('.xlsx', f'_backup_{backup_time}.xlsx')
                backup_path = os.path.join(backup_dir, backup_name)
                shutil.move(file, backup_path)
                logger.info(f"备份文件: {file} -> {backup_path}")
        else:
            logger.info("没有找到需要备份的汇总表文件")
    
    def find_date_column_in_row(self, df_raw, row_index=3):
        """
        在指定行中查找今天日期对应的列
        """
        if row_index >= len(df_raw):
            return None
        
        row_data = df_raw.iloc[row_index, :]
        target_date = self.excel_serial_today
        
        # 查找匹配的日期列
        for col_idx, value in enumerate(row_data):
            if isinstance(value, (int, float)) and not pd.isna(value):
                if abs(value - target_date) <= 1:  # 允许1天的误差
                    logger.info(f"找到日期列 {col_idx}: {value} (目标: {target_date})")
                    return col_idx
        
        # 如果没有找到精确匹配，查找最接近的
        date_candidates = []
        for col_idx, value in enumerate(row_data):
            if isinstance(value, (int, float)) and not pd.isna(value):
                if 45800 <= value <= 46000:  # 2025年左右的范围
                    date_candidates.append((col_idx, value))
        
        if date_candidates:
            # 找最接近今天的日期
            closest = min(date_candidates, key=lambda x: abs(x[1] - target_date))
            logger.warning(f"使用最接近的日期列 {closest[0]}: {closest[1]} (目标: {target_date}, 差距: {abs(closest[1] - target_date)} 天)")
            return closest[0]
        
        return None
    
    def unmerge_and_fill_cells(self, file_path, sheet_name):
        """
        处理指定sheet的合并单元格
        """
        try:
            wb = load_workbook(file_path)
            if sheet_name not in wb.sheetnames:
                logger.error(f"Sheet '{sheet_name}' 不存在")
                return None
            
            ws = wb[sheet_name]
            
            # 获取合并单元格范围
            merged_ranges = list(ws.merged_cells.ranges)
            
            # 记录合并单元格的值
            merge_values = {}
            for merged_range in merged_ranges:
                top_left_cell = ws.cell(merged_range.min_row, merged_range.min_col)
                value = top_left_cell.value
                
                for row in range(merged_range.min_row, merged_range.max_row + 1):
                    for col in range(merged_range.min_col, merged_range.max_col + 1):
                        merge_values[(row, col)] = value
            
            # 取消合并并填充值
            for merged_range in merged_ranges:
                ws.unmerge_cells(str(merged_range))
            
            for (row, col), value in merge_values.items():
                ws.cell(row, col, value)
            
            # 保存临时文件
            temp_file = f"temp_{sheet_name}_{self.today_str}.xlsx"
            wb.save(temp_file)
            return temp_file
            
        except Exception as e:
            logger.error(f"处理sheet '{sheet_name}' 的合并单元格失败: {e}")
            return None
    
    def process_sheet(self, file_path, sheet_name):
        """
        处理单个sheet，提取今天日期的数据
        """
        try:
            logger.info(f"正在处理sheet: {sheet_name}")
            
            # 处理合并单元格
            temp_file = self.unmerge_and_fill_cells(file_path, sheet_name)
            if temp_file is None:
                return None
            
            # 读取原始数据分析结构
            df_raw = pd.read_excel(temp_file, header=None, nrows=10)
            
            # 查找表头行（包含'序号', '监测类型', '居委', '环境类型'的行）
            header_row = None
            for i in range(15):  # 扩大搜索范围
                if i >= len(df_raw):
                    break
                row_values = [str(x) for x in df_raw.iloc[i, :8]]  # 检查前8列
                if '序号' in row_values and '监测类型' in row_values and '居委' in row_values and '环境类型' in row_values:
                    header_row = i
                    logger.info(f"找到表头行: {i}")
                    break

            if header_row is None:
                logger.error(f"Sheet '{sheet_name}' 中找不到表头行")
                logger.error(f"前10行数据: {[list(df_raw.iloc[i, :4]) for i in range(min(10, len(df_raw)))]}")
                os.remove(temp_file)
                return None
            
            # 查找今天日期对应的列（在表头行中查找）
            date_col_idx = self.find_date_column_in_row(df_raw, row_index=header_row)
            if date_col_idx is None:
                logger.error(f"Sheet '{sheet_name}' 中找不到今天日期的列")
                logger.error(f"表头行数据: {list(df_raw.iloc[header_row, :10])}")
                os.remove(temp_file)
                return None
            
            # 读取完整数据，跳过子表头行
            df = pd.read_excel(temp_file, header=header_row, skiprows=[header_row + 1])
            
            # 提取基本信息和今天的数据
            basic_cols = ['序号', '监测类型', '居委', '环境类型']
            
            # 找到今天日期列后面的监测数据列
            monitoring_cols = {}
            for offset in range(1, 10):
                if date_col_idx + offset >= len(df.columns):
                    break
                
                col_name = df.columns[date_col_idx + offset]
                col_str = str(col_name).lower()
                
                if offset == 1:  # 监测具体地址
                    continue
                elif offset == 2:  # 监测户数
                    monitoring_cols['监测户数'] = col_name
                elif offset == 3:  # 阳性数
                    monitoring_cols['阳性数'] = col_name
                elif offset == 4:  # 户外布雷图指数
                    monitoring_cols['户外布雷图指数（BI）'] = col_name
                elif offset == 5:  # 总雌蚊密度
                    monitoring_cols['总雌蚊密度（雌性ADI）'] = col_name
                elif '监测户数' in col_str:
                    monitoring_cols['监测户数'] = col_name
                elif '阳性数' in col_str:
                    monitoring_cols['阳性数'] = col_name
                elif '户外布雷图指数' in col_str or 'bi' in col_str:
                    monitoring_cols['户外布雷图指数（BI）'] = col_name
                elif '总雌蚊密度' in col_str or 'adi' in col_str:
                    monitoring_cols['总雌蚊密度（雌性ADI）'] = col_name
            
            if not monitoring_cols:
                logger.error(f"Sheet '{sheet_name}' 中找不到监测数据列")
                os.remove(temp_file)
                return None
            
            # 提取数据
            extract_cols = basic_cols + list(monitoring_cols.values())
            sheet_data = df[extract_cols].copy()
            
            # 重命名监测数据列
            rename_dict = {v: k for k, v in monitoring_cols.items()}
            sheet_data = sheet_data.rename(columns=rename_dict)
            
            # 添加镇街信息
            sheet_data['镇街'] = sheet_name
            sheet_data['日期'] = self.today.strftime('%Y-%m-%d')
            
            # 清理数据
            sheet_data = sheet_data.dropna(subset=['居委', '环境类型'])
            sheet_data['居委'] = sheet_data['居委'].astype(str).str.replace('\n', '').str.strip()
            
            # 清理临时文件
            os.remove(temp_file)
            
            logger.info(f"Sheet '{sheet_name}' 处理完成，提取 {len(sheet_data)} 行数据")
            return sheet_data
            
        except Exception as e:
            logger.error(f"处理sheet '{sheet_name}' 失败: {e}")
            return None
    
    def process_all_sheets(self, file_path):
        """
        处理所有sheet
        """
        try:
            wb = openpyxl.load_workbook(file_path)
            sheet_names = wb.sheetnames
            logger.info(f"发现 {len(sheet_names)} 个sheet")
            
            all_data = []
            successful_sheets = 0
            failed_sheets = []
            
            for i, sheet_name in enumerate(sheet_names, 1):
                print(f"[{i:2d}/{len(sheet_names)}] 处理sheet: {sheet_name}")
                
                sheet_data = self.process_sheet(file_path, sheet_name)
                if sheet_data is not None:
                    all_data.append(sheet_data)
                    successful_sheets += 1
                    
                    # 统计有效数据
                    valid_data = sheet_data[sheet_data['监测户数'] > 0] if '监测户数' in sheet_data.columns else pd.DataFrame()
                    print(f"    成功提取 {len(sheet_data)} 行数据，其中有效监测数据: {len(valid_data)} 行")
                else:
                    failed_sheets.append(sheet_name)
                    print(f"    处理失败")
            
            print(f"\n处理完成: 成功 {successful_sheets} 个，失败 {len(failed_sheets)} 个")
            if failed_sheets:
                print("失败的sheet:")
                for sheet in failed_sheets:
                    print(f"  - {sheet}")
            
            return all_data
            
        except Exception as e:
            logger.error(f"处理文件失败: {e}")
            return []

def main():
    """
    主函数
    """
    processor = MultiSheetProcessor()
    
    # 输入文件路径
    input_file = '/Users/<USER>/dev/wjj01/samples/24镇街表.xlsx'
    
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在: {input_file}")
        return
    
    print("24镇街表数据提取脚本")
    print("=" * 40)
    print(f"输入文件: {input_file}")
    print(f"提取日期: {processor.today.strftime('%Y年%m月%d日')}")
    print(f"Excel序列号: {processor.excel_serial_today}")
    print()
    
    # 备份现有文件
    processor.backup_existing_files()
    
    # 处理所有sheet
    all_data = processor.process_all_sheets(input_file)
    
    if not all_data:
        print("错误: 没有提取到任何数据")
        return
    
    # 合并所有数据
    print("\n正在合并数据...")
    combined_df = pd.concat(all_data, ignore_index=True)
    
    # 生成输出文件名
    output_file = f"汇总表_{processor.today_str}.xlsx"
    
    # 保存结果
    try:
        combined_df.to_excel(output_file, index=False)
        print(f"汇总表已保存: {output_file}")
        print(f"总数据行数: {len(combined_df)}")
        
        # 显示统计信息
        print("\n数据统计:")
        print(f"  镇街数量: {combined_df['镇街'].nunique()}")
        print(f"  居委数量: {combined_df['居委'].nunique()}")
        print(f"  环境类型数量: {combined_df['环境类型'].nunique()}")
        
        # 有效数据统计
        if '监测户数' in combined_df.columns:
            valid_data = combined_df[combined_df['监测户数'] > 0]
            print(f"  有效监测数据: {len(valid_data)} 行")
        
        print(f"\n处理完成！数据已保存到: {output_file}")
        
    except Exception as e:
        print(f"保存文件失败: {e}")

if __name__ == "__main__":
    main()
