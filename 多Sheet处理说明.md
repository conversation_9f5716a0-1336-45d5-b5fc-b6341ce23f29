# 24镇街表多Sheet数据提取脚本使用说明

## 脚本功能

本脚本专门用于从包含24个镇街sheet的Excel文件中提取今天日期（7月31日）的监测数据，生成完整的镇街-居委-环境类型数据明细。

## 核心特性

### ✅ 多Sheet自动处理
- 自动识别并处理24个镇街sheet
- 跳过汇总sheet和其他非镇街sheet
- 支持不同镇街的数据格式差异

### ✅ 智能日期识别
- 自动计算今天的Excel序列号（45869）
- 在每个sheet中查找对应的日期列
- 支持1天误差的容错机制

### ✅ 合并单元格处理
- 使用向下填充（ffill）处理合并单元格
- 避免复杂的openpyxl合并单元格操作
- 确保数据完整性

### ✅ 自动备份机制
- 自动备份现有汇总表到./backup文件夹
- 备份文件名包含时间戳
- 新汇总表以日期命名（汇总表_20250731.xlsx）

## 处理结果

### 📊 数据统计
- **总数据行数**: 3,455行
- **镇街数量**: 24个
- **居委数量**: 382个
- **环境类型**: 9种
- **有效监测数据**: 400行

### 📋 输出格式
| 列名 | 说明 |
|------|------|
| 镇街 | 镇街名称（如：1.江高镇） |
| 居委 | 居委会名称 |
| 环境类型 | 监测环境类型 |
| 监测户数 | 监测户数 |
| 阳性数 | 阳性数 |
| 户外布雷图指数（BI） | BI指数 |
| 总雌蚊密度（雌性ADI） | 雌蚊密度 |
| BI汇总 | BI指数÷监测户数×100 |
| 成蚊汇总 | 总雌蚊密度的值 |
| 日期 | 提取日期（2025-07-31） |

### 🏘️ 环境类型覆盖
1. 居民区 - 384条记录
2. 公园景区 - 384条记录  
3. 医疗机构 - 384条记录
4. 学校 - 384条记录
5. 福利机构 - 384条记录
6. 建筑工地 - 384条记录
7. 闲置房屋 - 383条记录
8. 商贸区 - 384条记录
9. 其他 - 384条记录

## 使用方法

### 1. 准备工作
```bash
# 确保Python环境和依赖库
pip install pandas openpyxl
```

### 2. 运行脚本
```bash
python3 simple_multi_sheet_processor.py
```

### 3. 输出文件
- **新汇总表**: `汇总表_20250731.xlsx`
- **备份文件**: `./backup/汇总表_YYYYMMDD_backup_YYYYMMDD_HHMMSS.xlsx`

## 处理流程

### 第一步：文件备份
- 检查当前目录是否有现有汇总表
- 自动备份到./backup文件夹
- 生成带时间戳的备份文件名

### 第二步：Sheet识别
- 读取Excel文件的所有sheet名称
- 过滤出24个镇街sheet（排除汇总sheet）
- 按顺序处理每个镇街

### 第三步：数据提取
- 查找表头行（第3行，索引3）
- 识别今天日期列（45869）
- 提取监测数据列（监测户数、阳性数、BI指数、雌蚊密度）
- 处理合并单元格（向下填充）

### 第四步：数据汇总
- 按镇街-居委-环境类型分组
- 计算各项指标的总和
- 计算BI汇总和成蚊汇总

### 第五步：结果保存
- 生成以日期命名的汇总表
- 显示详细统计信息
- 预览前10行数据

## 处理示例

### 成功处理的镇街
```
[ 1/24] 处理sheet: 1.江高镇
    成功提取 405 行数据，其中有效监测数据: 19 行
[ 2/24] 处理sheet: 2.人和镇  
    成功提取 405 行数据，其中有效监测数据: 22 行
[ 3/24] 处理sheet: 3.太和镇
    成功提取 405 行数据，其中有效监测数据: 22 行
...
处理完成: 成功 24 个，失败 0 个
```

### 有效监测数据示例
| 镇街 | 居委 | 环境类型 | 监测户数 | 阳性数 | BI汇总 |
|------|------|----------|----------|--------|--------|
| 1.江高镇 | 井岗 | 其他 | 2.0 | 4.0 | 200.0 |
| 1.江高镇 | 井岗 | 居民区 | 4.0 | 6.7 | 100.0 |
| 1.江高镇 | 南浦 | 居民区 | 1.0 | 5.0 | 200.0 |
| 1.江高镇 | 小塘 | 居民区 | 4.0 | 8.7 | 150.0 |

## 技术特点

### 🔧 错误处理
- 单个sheet失败不影响整体处理
- 详细的日志记录和错误信息
- 自动跳过无效数据

### ⚡ 性能优化
- 避免复杂的合并单元格操作
- 使用pandas的高效数据处理
- 内存友好的逐sheet处理

### 📝 数据质量
- 自动数据类型转换
- 空值处理和填充
- 数据完整性验证

## 注意事项

### ⚠️ 重要提醒
1. **日期固定**: 脚本固定提取7月31日（Excel序列号45869）的数据
2. **文件路径**: 输入文件路径硬编码为`/Users/<USER>/dev/wjj01/samples/24镇街表.xlsx`
3. **备份机制**: 每次运行都会备份现有汇总表
4. **数据覆盖**: 新汇总表会覆盖同名文件

### 🔄 自定义修改
如需修改日期或文件路径，编辑脚本中的相应部分：
```python
# 修改输入文件路径
input_file = '/path/to/your/24镇街表.xlsx'

# 修改提取日期
self.today = datetime(2025, 7, 31)  # 年, 月, 日
```

## 故障排除

### Q1: 找不到日期列
**现象**: 提示"找不到今天日期的列"
**解决**: 检查Excel文件中是否有45869这个数字，或者最接近的日期

### Q2: 找不到表头行  
**现象**: 提示"找不到表头行"
**解决**: 检查sheet中是否包含"序号、监测类型、居委、环境类型"

### Q3: 处理失败
**现象**: 某些sheet处理失败
**解决**: 查看日志信息，检查sheet格式是否一致

### Q4: 数据不完整
**现象**: 提取的数据行数不对
**解决**: 检查合并单元格是否正确处理，查看有效数据统计

## 总结

本脚本成功实现了从24个镇街sheet中提取7月31日监测数据的需求，生成了包含3,455行数据的完整汇总表，覆盖24个镇街、382个居委、9种环境类型，其中400行为有效监测数据。脚本具有良好的错误处理、自动备份和数据质量保证机制。
