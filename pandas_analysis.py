#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用pandas重新分析白云区蚊媒监测重点场所监测情况
正确理解统计方法：监测总数是指监测户数大于0的居委-环境类型记录行的总数
"""

import pandas as pd
import numpy as np
from pathlib import Path

def load_and_analyze_data(file_path):
    """加载并分析Excel数据"""
    try:
        # 读取汇总表
        df = pd.read_excel(file_path, sheet_name='汇总', header=None)
        print(f"成功加载汇总表，数据形状: {df.shape}")
        
        # 显示前几行来理解结构
        print("\n前10行数据预览:")
        print(df.head(10))
        
        return df
    except Exception as e:
        print(f"加载Excel文件失败: {e}")
        return None

def identify_structure(df):
    """识别数据结构"""
    print("\n=== 数据结构分析 ===")
    
    # 查找表头行
    header_rows = []
    for idx in range(min(10, len(df))):
        row_content = df.iloc[idx].astype(str).str.contains('物业小区|建筑工地|公园景区', na=False).any()
        if row_content:
            header_rows.append(idx)
            print(f"找到包含环境类型的行: {idx}")
            print(f"行内容: {df.iloc[idx].tolist()[:20]}")  # 只显示前20列
    
    # 查找数据开始行
    data_start_row = None
    for idx in range(5, min(15, len(df))):
        # 查找第一个包含镇街名称的数据行
        district_col = df.iloc[idx, 1]  # B列通常是镇街列
        if pd.notna(district_col) and str(district_col) not in ['全区情况', '区/镇街', '']:
            data_start_row = idx
            print(f"找到数据开始行: {idx}, 镇街: {district_col}")
            break
    
    return header_rows, data_start_row

def analyze_environment_columns(df, header_rows):
    """分析环境类型列的位置"""
    print("\n=== 环境类型列位置分析 ===")
    
    # 8个环境类型
    environments = [
        '物业小区', '建筑工地', '公园景区', '学校', 
        '福利机构', '医疗机构', '农贸市场', '其它场所'
    ]
    
    env_columns = {}
    
    # 在表头行中查找环境类型
    for header_row_idx in header_rows:
        header_row = df.iloc[header_row_idx]
        for col_idx, cell_value in enumerate(header_row):
            cell_str = str(cell_value) if pd.notna(cell_value) else ""
            for env in environments:
                if env in cell_str:
                    if env not in env_columns:
                        env_columns[env] = []
                    # 每个环境类型通常有4列：总数、中高风险占比、高风险、中风险
                    env_columns[env] = [col_idx, col_idx+1, col_idx+2, col_idx+3]
                    break
    
    print("环境类型列位置:")
    for env, cols in env_columns.items():
        print(f"  {env}: {cols}")
    
    return env_columns

def extract_monitoring_data(df, data_start_row, env_columns):
    """提取监测数据"""
    print("\n=== 提取监测数据 ===")
    
    results = {}
    
    for env_name, cols in env_columns.items():
        if len(cols) < 4:
            continue
            
        total_col, ratio_col, high_risk_col, medium_risk_col = cols
        
        print(f"\n分析 {env_name}:")
        print(f"  列位置 - 总数:{total_col}, 中高风险占比:{ratio_col}, 高风险:{high_risk_col}, 中风险:{medium_risk_col}")
        
        env_data = []
        
        # 遍历数据行
        for row_idx in range(data_start_row, len(df)):
            district = df.iloc[row_idx, 1]  # B列：镇街
            monitor_type = df.iloc[row_idx, 3]  # D列：监测类型
            
            if pd.notna(district) and pd.notna(monitor_type) and str(district) != '全区情况':
                total = df.iloc[row_idx, total_col]
                high_risk = df.iloc[row_idx, high_risk_col] 
                medium_risk = df.iloc[row_idx, medium_risk_col]
                
                # 检查是否有监测数据（总数>0）
                if pd.notna(total) and isinstance(total, (int, float)) and total > 0:
                    env_data.append({
                        'district': district,
                        'monitor_type': monitor_type,
                        'total': total,
                        'high_risk': high_risk if pd.notna(high_risk) else 0,
                        'medium_risk': medium_risk if pd.notna(medium_risk) else 0
                    })
        
        # 重新理解统计方法：
        # 监测总数 = 各镇街该环境类型"总数"字段的总和（这个总数已经是居委-环境类型记录行的总数）
        # 高风险总数 = 各镇街该环境类型"高风险"字段的总和
        # 中风险总数 = 各镇街该环境类型"中风险"字段的总和

        total_count = sum(item['total'] for item in env_data if isinstance(item['total'], (int, float)))
        high_risk_count = sum(item['high_risk'] for item in env_data if isinstance(item['high_risk'], (int, float)))
        medium_risk_count = sum(item['medium_risk'] for item in env_data if isinstance(item['medium_risk'], (int, float)))
        medium_high_risk_count = high_risk_count + medium_risk_count
        risk_ratio = (medium_high_risk_count / total_count * 100) if total_count > 0 else 0
        
        results[env_name] = {
            'total_count': total_count,
            'high_risk_count': high_risk_count,
            'medium_risk_count': medium_risk_count,
            'medium_high_risk_ratio': round(risk_ratio, 1),
            'data': env_data
        }
        
        print(f"  统计结果: 总数={total_count}, 高风险={high_risk_count}, 中风险={medium_risk_count}, 中高风险占比={risk_ratio:.1f}%")
    
    return results

def display_results(results):
    """显示分析结果"""
    print("\n" + "="*100)
    print("白云区蚊媒监测重点场所监测情况统计分析结果")
    print("="*100)
    print("统计方法：监测总数是指监测户数大于0的镇街-监测类型-环境类型记录行的总数")
    print("-"*100)
    
    # 汇总表格
    print(f"\n{'环境类型':<15} {'总数':<8} {'中高风险占比':<12} {'高风险':<8} {'中风险':<8}")
    print("-" * 70)
    
    # 按照指定顺序显示
    priority_envs = ['物业小区', '建筑工地', '公园景区']
    other_envs = ['学校', '福利机构', '医疗机构', '农贸市场', '其它场所']
    
    for env in priority_envs + other_envs:
        if env in results:
            data = results[env]
            print(f"{env:<15} {data['total_count']:<8} {data['medium_high_risk_ratio']:<12}% {data['high_risk_count']:<8} {data['medium_risk_count']:<8}")
    
    # 详细分析
    print(f"\n{'='*100}")
    print("详细分析")
    print("="*100)
    
    for env in priority_envs:
        if env in results:
            data = results[env]
            print(f"\n【{env}】")
            print(f"  总数: {data['total_count']}")
            print(f"  中高风险占比: {data['medium_high_risk_ratio']}%")
            print(f"  高风险: {data['high_risk_count']}")
            print(f"  中风险: {data['medium_risk_count']}")
            
            if data['data']:
                print(f"  详细记录:")
                for i, record in enumerate(data['data'][:10], 1):  # 只显示前10条
                    risk_status = ""
                    if isinstance(record['high_risk'], (int, float)) and record['high_risk'] > 0:
                        risk_status = " [高风险]"
                    elif isinstance(record['medium_risk'], (int, float)) and record['medium_risk'] > 0:
                        risk_status = " [中风险]"
                    else:
                        risk_status = " [低风险/风险可控]"
                    
                    print(f"    {i:2d}. {record['district']:<12} - {record['monitor_type']:<6} - "
                          f"监测点数:{record['total']:3}, 高风险:{record['high_risk']:2}, "
                          f"中风险:{record['medium_risk']:2}{risk_status}")
            print("-" * 80)

def main():
    """主函数"""
    file_path = "/Users/<USER>/dev/wjj01/白云区蚊媒监测一览表_完整版_20250731.xlsx"
    
    if not Path(file_path).exists():
        print(f"文件不存在: {file_path}")
        return
    
    print(f"开始分析文件: {file_path}")
    
    # 1. 加载数据
    df = load_and_analyze_data(file_path)
    if df is None:
        return
    
    # 2. 识别数据结构
    header_rows, data_start_row = identify_structure(df)
    if not header_rows or data_start_row is None:
        print("无法识别数据结构")
        return
    
    # 3. 分析环境类型列位置
    env_columns = analyze_environment_columns(df, header_rows)
    if not env_columns:
        print("无法识别环境类型列")
        return
    
    # 4. 提取监测数据
    results = extract_monitoring_data(df, data_start_row, env_columns)
    
    # 5. 显示结果
    display_results(results)

if __name__ == "__main__":
    main()
