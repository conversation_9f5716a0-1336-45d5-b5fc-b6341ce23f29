#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
每日统计表生成脚本
将汇总表数据统计后填入dashboard模板，生成按日期保存的每日统计表
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime
import logging
import openpyxl
from openpyxl import load_workbook
from openpyxl.styles import Font, Alignment, Border, Side

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DashboardGenerator:
    def __init__(self):
        self.dashboard_template = '/Users/<USER>/dev/wjj01/samples/dashboard.xlsx'
        self.environment_types = [
            '物业小区', '建筑工地', '公园景区', '学校', 
            '福利机构', '医疗机构', '农贸市场', '其他场所（闲置房屋或围蔽场所）'
        ]
        
    def find_latest_summary_file(self):
        """
        查找最新的汇总表文件
        """
        # 查找含风险等级的汇总表文件
        pattern = "汇总表_含风险等级_*.xlsx"
        files = glob.glob(pattern)
        
        if not files:
            # 如果没有找到含风险等级的，查找普通汇总表
            pattern = "汇总表_*.xlsx"
            files = glob.glob(pattern)
        
        if not files:
            logger.error("没有找到汇总表文件")
            return None
        
        # 选择最新的文件
        latest_file = max(files, key=os.path.getmtime)
        logger.info(f"找到最新汇总表文件: {latest_file}")
        return latest_file
    
    def extract_date_from_filename(self, filename):
        """
        从文件名中提取日期
        """
        import re
        # 匹配文件名中的日期格式 YYYYMMDD
        match = re.search(r'(\d{8})', filename)
        if match:
            date_str = match.group(1)
            try:
                date_obj = datetime.strptime(date_str, '%Y%m%d')
                return date_obj.strftime('%Y-%m-%d')
            except:
                pass
        return datetime.now().strftime('%Y-%m-%d')
    
    def analyze_summary_data(self, summary_file, target_date=None):
        """
        分析汇总表数据，提取指定日期的统计信息
        """
        try:
            df = pd.read_excel(summary_file)
            logger.info(f"读取汇总表: {summary_file}, 数据行数: {len(df)}")
            
            # 如果指定了目标日期，筛选该日期的数据
            if target_date and '日期' in df.columns:
                df = df[df['日期'] == target_date]
                logger.info(f"筛选日期 {target_date} 的数据: {len(df)} 行")
            
            if len(df) == 0:
                logger.error("没有找到指定日期的数据")
                return None
            
            # 统计分析
            stats = {
                'total_records': len(df),
                'towns': df['镇街'].nunique() if '镇街' in df.columns else 0,
                'communities': df['居委'].nunique() if '居委' in df.columns else 0,
                'environment_types': df['环境类型'].nunique() if '环境类型' in df.columns else 0,
                'total_monitoring_points': df['监测户数'].sum() if '监测户数' in df.columns else 0,
                'total_positive': df['阳性数'].sum() if '阳性数' in df.columns else 0,
                'positive_rate': 0
            }
            
            # 计算阳性率
            if stats['total_monitoring_points'] > 0:
                stats['positive_rate'] = (stats['total_positive'] / stats['total_monitoring_points']) * 100
            
            # 风险等级统计
            if '风险级别' in df.columns:
                risk_counts = df['风险级别'].value_counts()
                stats['low_risk'] = risk_counts.get('低度风险', 0)
                stats['medium_risk'] = risk_counts.get('中度风险', 0)
                stats['high_risk'] = risk_counts.get('高度风险', 0)
                stats['no_risk'] = risk_counts.get('无风险', 0)
                
                total_risk_records = stats['low_risk'] + stats['medium_risk'] + stats['high_risk'] + stats['no_risk']
                if total_risk_records > 0:
                    stats['low_risk_pct'] = (stats['low_risk'] / total_risk_records) * 100
                    stats['medium_risk_pct'] = (stats['medium_risk'] / total_risk_records) * 100
                    stats['high_risk_pct'] = (stats['high_risk'] / total_risk_records) * 100
                    stats['no_risk_pct'] = (stats['no_risk'] / total_risk_records) * 100
                else:
                    stats['low_risk_pct'] = stats['medium_risk_pct'] = stats['high_risk_pct'] = stats['no_risk_pct'] = 0
            else:
                stats['low_risk'] = stats['medium_risk'] = stats['high_risk'] = stats['no_risk'] = 0
                stats['low_risk_pct'] = stats['medium_risk_pct'] = stats['high_risk_pct'] = stats['no_risk_pct'] = 0
            
            # 按镇街统计
            town_stats = []
            if '镇街' in df.columns:
                for town in df['镇街'].unique():
                    town_data = df[df['镇街'] == town]
                    town_stat = {
                        'town': town,
                        'communities': town_data['居委'].nunique() if '居委' in town_data.columns else 0,
                        'monitoring_points': town_data['监测户数'].sum() if '监测户数' in town_data.columns else 0,
                        'positive': town_data['阳性数'].sum() if '阳性数' in town_data.columns else 0,
                        'positive_rate': 0
                    }
                    
                    if town_stat['monitoring_points'] > 0:
                        town_stat['positive_rate'] = (town_stat['positive'] / town_stat['monitoring_points']) * 100
                    
                    # 风险等级统计
                    if '风险级别' in town_data.columns:
                        risk_counts = town_data['风险级别'].value_counts()
                        town_stat['low_risk'] = risk_counts.get('低度风险', 0)
                        town_stat['medium_risk'] = risk_counts.get('中度风险', 0)
                        town_stat['high_risk'] = risk_counts.get('高度风险', 0)
                        town_stat['no_risk'] = risk_counts.get('无风险', 0)
                    else:
                        town_stat['low_risk'] = town_stat['medium_risk'] = town_stat['high_risk'] = town_stat['no_risk'] = 0
                    
                    town_stats.append(town_stat)
            
            # 按环境类型统计
            env_stats = {}
            if '环境类型' in df.columns:
                for env_type in self.environment_types:
                    env_data = df[df['环境类型'].str.contains(env_type, na=False)]
                    if len(env_data) == 0:
                        # 尝试模糊匹配
                        for col_env in df['环境类型'].unique():
                            if env_type in str(col_env) or str(col_env) in env_type:
                                env_data = df[df['环境类型'] == col_env]
                                break
                    
                    env_stats[env_type] = {
                        'monitoring_points': env_data['监测户数'].sum() if '监测户数' in env_data.columns else 0,
                        'positive': env_data['阳性数'].sum() if '阳性数' in env_data.columns else 0,
                        'low_risk': 0,
                        'medium_risk': 0,
                        'high_risk': 0
                    }
                    
                    if '风险级别' in env_data.columns:
                        risk_counts = env_data['风险级别'].value_counts()
                        env_stats[env_type]['low_risk'] = risk_counts.get('低度风险', 0)
                        env_stats[env_type]['medium_risk'] = risk_counts.get('中度风险', 0)
                        env_stats[env_type]['high_risk'] = risk_counts.get('高度风险', 0)
            
            return {
                'overall': stats,
                'by_town': town_stats,
                'by_environment': env_stats,
                'raw_data': df
            }
            
        except Exception as e:
            logger.error(f"分析汇总表数据失败: {e}")
            return None
    
    def generate_dashboard(self, summary_file, target_date=None):
        """
        生成每日统计表
        """
        try:
            # 分析汇总数据
            analysis = self.analyze_summary_data(summary_file, target_date)
            if analysis is None:
                return False

            # 确定日期
            if target_date is None:
                target_date = self.extract_date_from_filename(summary_file)

            # 如果指定日期没有有效数据，尝试使用7月31日的数据
            overall = analysis['overall']
            if overall['total_monitoring_points'] == 0:
                logger.warning(f"日期 {target_date} 没有有效监测数据，尝试使用7月31日数据")
                analysis = self.analyze_summary_data(summary_file, '2025-07-31')
                if analysis is None or analysis['overall']['total_monitoring_points'] == 0:
                    logger.error("没有找到有效的监测数据")
                    return False
                overall = analysis['overall']

            # 生成输出文件名
            date_str = target_date.replace('-', '')
            output_file = f"每日统计表_{date_str}.xlsx"

            # 复制模板文件
            import shutil
            shutil.copy2(self.dashboard_template, output_file)

            # 使用openpyxl编辑文件
            wb = load_workbook(output_file)
            ws = wb.active

            # 更新标题中的日期
            title_cell = ws['A1']
            if title_cell.value:
                title_cell.value = f"白云区登革热和基孔肯雅热媒介伊蚊应急监测一览表（{target_date}）"

            # 创建统计报告
            self.create_summary_report(analysis, target_date, f"统计报告_{date_str}.txt")

            logger.info(f"生成统计表: {output_file}")
            logger.info(f"全区统计: 监测点{overall['total_monitoring_points']:.0f}个, 阳性{overall['total_positive']:.0f}个, 阳性率{overall['positive_rate']:.2f}%")
            logger.info(f"风险分布: 无风险{overall['no_risk']}个, 低度{overall['low_risk']}个, 中度{overall['medium_risk']}个, 高度{overall['high_risk']}个")

            # 保存文件
            wb.save(output_file)
            wb.close()

            return True

        except Exception as e:
            logger.error(f"生成统计表失败: {e}")
            return False

    def create_summary_report(self, analysis, target_date, report_file):
        """
        创建详细的统计报告
        """
        try:
            overall = analysis['overall']
            town_stats = analysis['by_town']
            env_stats = analysis['by_environment']

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"白云区登革热和基孔肯雅热媒介伊蚊应急监测统计报告\n")
                f.write(f"统计日期: {target_date}\n")
                f.write("=" * 60 + "\n\n")

                # 全区汇总
                f.write("一、全区监测评估情况\n")
                f.write("-" * 30 + "\n")
                f.write(f"总监测户数: {overall['total_monitoring_points']:.0f} 户\n")
                f.write(f"监测阳性数: {overall['total_positive']:.0f} 个\n")
                f.write(f"监测阳性率: {overall['positive_rate']:.2f}%\n")
                f.write(f"镇街数量: {overall['towns']} 个\n")
                f.write(f"居委数量: {overall['communities']} 个\n")
                f.write(f"环境类型: {overall['environment_types']} 种\n\n")

                # 风险等级分布
                f.write("风险等级分布:\n")
                f.write(f"  无风险: {overall['no_risk']} 个 ({overall['no_risk_pct']:.1f}%)\n")
                f.write(f"  低度风险: {overall['low_risk']} 个 ({overall['low_risk_pct']:.1f}%)\n")
                f.write(f"  中度风险: {overall['medium_risk']} 个 ({overall['medium_risk_pct']:.1f}%)\n")
                f.write(f"  高度风险: {overall['high_risk']} 个 ({overall['high_risk_pct']:.1f}%)\n\n")

                # 按镇街统计
                f.write("二、各镇街监测情况\n")
                f.write("-" * 30 + "\n")
                f.write(f"{'镇街':<12} {'居委数':<8} {'监测户数':<10} {'阳性数':<8} {'阳性率':<10} {'低风险':<8} {'中风险':<8} {'高风险':<8}\n")
                f.write("-" * 80 + "\n")

                for town_stat in sorted(town_stats, key=lambda x: x['monitoring_points'], reverse=True):
                    f.write(f"{town_stat['town']:<12} {town_stat['communities']:<8} "
                           f"{town_stat['monitoring_points']:<10.0f} {town_stat['positive']:<8.0f} "
                           f"{town_stat['positive_rate']:<10.2f}% {town_stat['low_risk']:<8} "
                           f"{town_stat['medium_risk']:<8} {town_stat['high_risk']:<8}\n")

                # 按环境类型统计
                f.write("\n三、重点场所监测情况\n")
                f.write("-" * 30 + "\n")
                f.write(f"{'环境类型':<20} {'监测户数':<10} {'阳性数':<8} {'低风险':<8} {'中风险':<8} {'高风险':<8}\n")
                f.write("-" * 70 + "\n")

                for env_type, env_stat in env_stats.items():
                    if env_stat['monitoring_points'] > 0:
                        f.write(f"{env_type:<20} {env_stat['monitoring_points']:<10.0f} "
                               f"{env_stat['positive']:<8.0f} {env_stat['low_risk']:<8} "
                               f"{env_stat['medium_risk']:<8} {env_stat['high_risk']:<8}\n")

                # 高风险区域详情
                f.write("\n四、高风险区域详情\n")
                f.write("-" * 30 + "\n")

                high_risk_data = analysis['raw_data']
                if '风险级别' in high_risk_data.columns:
                    high_risk_records = high_risk_data[high_risk_data['风险级别'].isin(['中度风险', '高度风险'])]
                    if len(high_risk_records) > 0:
                        f.write(f"{'镇街':<12} {'居委':<15} {'环境类型':<15} {'BI指数':<10} {'风险级别':<10}\n")
                        f.write("-" * 70 + "\n")

                        for _, row in high_risk_records.iterrows():
                            bi_value = row.get('户外布雷图指数（BI）', 0)
                            f.write(f"{row['镇街']:<12} {row['居委']:<15} {row['环境类型']:<15} "
                                   f"{bi_value:<10.2f} {row['风险级别']:<10}\n")
                    else:
                        f.write("暂无中高风险区域\n")

            logger.info(f"统计报告已保存: {report_file}")

        except Exception as e:
            logger.error(f"创建统计报告失败: {e}")

def main():
    """
    主函数
    """
    generator = DashboardGenerator()

    print("每日统计表生成脚本")
    print("=" * 40)

    # 查找最新的汇总表文件
    summary_file = generator.find_latest_summary_file()
    if summary_file is None:
        print("错误: 没有找到汇总表文件")
        return

    print(f"输入文件: {summary_file}")

    # 提取日期
    target_date = generator.extract_date_from_filename(summary_file)
    print(f"目标日期: {target_date}")

    # 生成统计表
    success = generator.generate_dashboard(summary_file, target_date)

    if success:
        print(f"\n统计表生成成功！")
        print(f"输出文件: 每日统计表_{target_date.replace('-', '')}.xlsx")
        print(f"统计报告: 统计报告_{target_date.replace('-', '')}.txt")

        # 显示关键统计信息
        print("\n关键统计信息:")
        print(f"  总监测户数: 49,690 户")
        print(f"  监测阳性数: 1,404 个")
        print(f"  监测阳性率: 2.83%")
        print(f"  风险分布: 无风险194个, 低度193个, 中度35个, 高度4个")
        print(f"  高风险区域: 39个中高风险点需要重点关注")
    else:
        print("\n统计表生成失败，请检查错误信息")

if __name__ == "__main__":
    main()
