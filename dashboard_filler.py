#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dashboard填充脚本
从最新的汇总表中读取数据并填入dashboard.xlsx的Sheet1
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime, timedelta
import logging
from openpyxl import load_workbook

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DashboardFiller:
    def __init__(self):
        self.dashboard_path = '/Users/<USER>/dev/wjj01/samples/dashboard.xlsx'
        self.excel_epoch = datetime(1900, 1, 1)
        
    def excel_serial_to_date(self, serial_number):
        """将Excel序列号转换为日期"""
        return self.excel_epoch + timedelta(days=int(serial_number) - 2)
    
    def find_latest_summary_file(self):
        """查找最新的汇总表文件"""
        patterns = [
            "汇总表_含风险等级_*.xlsx",
            "汇总表_所有日期_*.xlsx", 
            "汇总表_新结构_*.xlsx",
            "汇总表_*.xlsx"
        ]
        
        all_files = []
        for pattern in patterns:
            files = glob.glob(pattern)
            all_files.extend(files)
        
        if not all_files:
            return None
        
        # 选择最新的文件
        latest_file = max(all_files, key=os.path.getmtime)
        logger.info(f"找到最新汇总表: {latest_file}")
        return latest_file
    
    def load_summary_data(self, file_path):
        """加载汇总表数据"""
        try:
            df = pd.read_excel(file_path)
            logger.info(f"成功加载汇总表: {len(df)} 行数据")
            
            # 只保留有效数据
            if '监测户数' in df.columns:
                valid_df = df[df['监测户数'] > 0].copy()
                logger.info(f"有效监测数据: {len(valid_df)} 行")
                return valid_df
            
            return df
            
        except Exception as e:
            logger.error(f"加载汇总表失败: {e}")
            return None
    
    def analyze_dashboard_dates(self, ws):
        """分析dashboard中的日期列"""
        date_columns = {}
        
        # 扫描第2行的日期
        for col in range(6, 30):  # F列到AD列
            try:
                cell_value = ws.cell(row=2, column=col).value
                if isinstance(cell_value, (int, float)) and 45800 <= cell_value <= 46000:
                    date_obj = self.excel_serial_to_date(cell_value)
                    date_str = date_obj.strftime('%Y-%m-%d')
                    
                    # 确定数据列位置（基于第3行的表头）
                    data_cols = {}
                    for offset in range(5):
                        check_col = col + offset
                        if check_col <= ws.max_column:
                            header = ws.cell(row=3, column=check_col).value
                            if header:
                                header_str = str(header).strip()
                                if '监测户数' in header_str:
                                    data_cols['监测户数'] = check_col
                                elif '阳性数' in header_str:
                                    data_cols['阳性数'] = check_col
                                elif '户外布雷图指数' in header_str or 'BI' in header_str:
                                    data_cols['户外布雷图指数（BI）'] = check_col
                                elif '总雌蚊密度' in header_str or 'ADI' in header_str:
                                    data_cols['总雌蚊密度（雌性ADI）'] = check_col
                    
                    if data_cols:
                        date_columns[date_str] = data_cols
                        logger.info(f"找到日期 {date_str}: {list(data_cols.keys())}")
            except:
                continue
        
        return date_columns
    
    def build_row_mapping(self, ws):
        """构建居委-环境类型到行号的映射"""
        row_mapping = {}
        current_juwei = None
        
        for row in range(4, ws.max_row + 1):
            # B列是居委
            juwei_cell = ws.cell(row=row, column=2)
            if juwei_cell.value:
                current_juwei = str(juwei_cell.value).strip()
            
            # D列是环境类型
            env_cell = ws.cell(row=row, column=4)
            if env_cell.value and current_juwei:
                env_type = str(env_cell.value).strip()
                key = f"{current_juwei}_{env_type}"
                row_mapping[key] = row
        
        logger.info(f"构建行映射: {len(row_mapping)} 个组合")
        return row_mapping
    
    def map_environment_type(self, env_type):
        """映射环境类型名称"""
        mapping = {
            '物业小区': '居民区',
            '其他场所（闲置房屋或围蔽场所）': '其他',
            '农贸市场': '商贸区',
            '闲置房屋': '其他'
        }
        return mapping.get(env_type, env_type)
    
    def create_dashboard_from_template(self, summary_df):
        """基于模板创建新的dashboard"""
        try:
            # 加载dashboard模板
            wb = load_workbook(self.dashboard_path)
            ws = wb['Sheet1']

            # 分析模板结构
            date_columns = self.analyze_dashboard_dates(ws)
            if not date_columns:
                logger.error("Dashboard模板中没有找到有效的日期列")
                return False, None, 0, 0

            # 获取所有需要的居委和环境类型组合
            combinations = []
            for _, row in summary_df.iterrows():
                juwei = str(row['居委']).strip()
                env_type = str(row['环境类型']).strip()
                mapped_env = self.map_environment_type(env_type)

                combo = (juwei, mapped_env)
                if combo not in combinations:
                    combinations.append(combo)

            logger.info(f"需要创建 {len(combinations)} 个居委-环境类型组合")

            # 取消所有合并单元格
            merged_ranges = list(ws.merged_cells.ranges)
            for merged_range in merged_ranges:
                ws.unmerge_cells(str(merged_range))

            # 清空现有数据行（保留表头）
            for row in range(4, ws.max_row + 1):
                for col in range(1, ws.max_column + 1):
                    try:
                        ws.cell(row=row, column=col).value = None
                    except:
                        pass  # 跳过无法修改的单元格

            # 创建新的数据行
            current_row = 4
            row_mapping = {}

            for i, (juwei, env_type) in enumerate(combinations, 1):
                # 创建该居委的所有环境类型行
                env_types = ['居民区', '公园景区', '医疗机构', '学校', '福利机构', '建筑工地', '闲置房屋', '商贸区', '其他']

                for env in env_types:
                    # A列：序号
                    ws.cell(row=current_row, column=1).value = i
                    # B列：居委
                    ws.cell(row=current_row, column=2).value = juwei
                    # C列：监测点（空）
                    ws.cell(row=current_row, column=3).value = ""
                    # D列：环境类型
                    ws.cell(row=current_row, column=4).value = env
                    # E列：监测具体地址（空）
                    ws.cell(row=current_row, column=5).value = ""

                    # 记录行映射
                    key = f"{juwei}_{env}"
                    row_mapping[key] = current_row

                    current_row += 1

            logger.info(f"创建了 {len(row_mapping)} 个数据行")

            # 填入数据
            logger.info("填入监测数据...")
            updated_count = 0
            matched_count = 0

            for _, row_data in summary_df.iterrows():
                juwei = str(row_data['居委']).strip()
                env_type = str(row_data['环境类型']).strip()
                mapped_env = self.map_environment_type(env_type)
                key = f"{juwei}_{mapped_env}"

                if key not in row_mapping:
                    continue

                matched_count += 1
                target_row = row_mapping[key]

                # 获取日期
                date_str = None
                if '日期' in row_data and pd.notna(row_data['日期']):
                    date_str = str(row_data['日期'])

                if not date_str or date_str not in date_columns:
                    # 使用第一个可用日期
                    if date_columns:
                        date_str = list(date_columns.keys())[0]
                    else:
                        continue

                # 填入数据
                date_cols = date_columns[date_str]
                for field_name, col_idx in date_cols.items():
                    if field_name in row_data and pd.notna(row_data[field_name]):
                        value = row_data[field_name]
                        if isinstance(value, (int, float)) and not pd.isna(value):
                            ws.cell(row=target_row, column=col_idx).value = float(value)
                            updated_count += 1

            logger.info(f"匹配的记录: {matched_count}")
            logger.info(f"更新的数据点: {updated_count}")

            # 保存文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = f"dashboard_created_{timestamp}.xlsx"
            wb.save(output_path)
            logger.info(f"Dashboard已保存: {output_path}")

            return True, output_path, matched_count, updated_count

        except Exception as e:
            logger.error(f"创建dashboard失败: {e}")
            return False, None, 0, 0

def main():
    """主函数"""
    filler = DashboardFiller()
    
    print("Dashboard填充脚本")
    print("=" * 40)
    
    # 查找最新汇总表
    summary_file = filler.find_latest_summary_file()
    if not summary_file:
        print("❌ 错误: 没有找到汇总表文件")
        return
    
    print(f"📊 数据源: {summary_file}")
    
    # 加载汇总数据
    summary_df = filler.load_summary_data(summary_file)
    if summary_df is None:
        print("❌ 错误: 加载汇总数据失败")
        return
    
    # 检查dashboard文件
    if not os.path.exists(filler.dashboard_path):
        print(f"❌ 错误: Dashboard文件不存在: {filler.dashboard_path}")
        return
    
    print(f"📋 Dashboard模板: {filler.dashboard_path}")
    
    # 创建dashboard
    success, output_path, matched_count, updated_count = filler.create_dashboard_from_template(summary_df)
    
    if success:
        print(f"\n✅ Dashboard填充成功!")
        print(f"📁 输出文件: {output_path}")
        print(f"📊 匹配记录: {matched_count}")
        print(f"📈 更新数据点: {updated_count}")
        
        # 显示数据概况
        print(f"\n📋 数据概况:")
        print(f"  总记录数: {len(summary_df)}")
        print(f"  镇街数量: {summary_df['镇街'].nunique()}")
        print(f"  居委数量: {summary_df['居委'].nunique()}")
        print(f"  环境类型数量: {summary_df['环境类型'].nunique()}")
        
        if '日期' in summary_df.columns:
            dates = sorted(summary_df['日期'].unique())
            print(f"  日期范围: {dates[0]} 到 {dates[-1]}")
        
    else:
        print("❌ Dashboard填充失败")

if __name__ == "__main__":
    main()
