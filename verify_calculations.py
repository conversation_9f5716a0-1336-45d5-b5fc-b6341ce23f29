#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证BI汇总和成蚊汇总计算逻辑的脚本
"""

import pandas as pd
import numpy as np

def verify_calculations(file_path):
    """
    验证汇总表中BI汇总和成蚊汇总的计算是否正确
    """
    print("=== 验证BI汇总和成蚊汇总计算逻辑 ===")
    print(f"读取文件: {file_path}")
    
    try:
        df = pd.read_excel(file_path)
        print(f"数据行数: {len(df)}")
        print(f"数据列数: {len(df.columns)}")
        print()
        
        # 检查必要的列是否存在
        required_cols = ['监测户数', '户外布雷图指数（BI）', '总雌蚊密度（雌性ADI）', 'BI汇总', '成蚊汇总']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"错误: 缺少必要的列: {missing_cols}")
            return False
        
        print("✅ 所有必要的列都存在")
        print()
        
        # 验证BI汇总计算
        print("=== 验证BI汇总计算 ===")
        errors = 0
        
        for i, row in df.iterrows():
            监测户数 = row['监测户数']
            BI指数 = row['户外布雷图指数（BI）']
            BI汇总_实际 = row['BI汇总']
            
            # 计算期望的BI汇总
            if 监测户数 > 0:
                BI汇总_期望 = BI指数 / 监测户数 * 100
            else:
                BI汇总_期望 = 0
            
            # 检查是否相等（允许小的浮点误差）
            if abs(BI汇总_实际 - BI汇总_期望) > 0.001:
                print(f"❌ 第{i+1}行 BI汇总计算错误:")
                print(f"   监测户数: {监测户数}, BI指数: {BI指数}")
                print(f"   实际BI汇总: {BI汇总_实际}, 期望BI汇总: {BI汇总_期望}")
                errors += 1
                
                if errors >= 5:  # 只显示前5个错误
                    print("   ... (更多错误)")
                    break
        
        if errors == 0:
            print("✅ 所有BI汇总计算都正确")
        else:
            print(f"❌ 发现 {errors} 个BI汇总计算错误")
        
        print()
        
        # 验证成蚊汇总计算
        print("=== 验证成蚊汇总计算 ===")
        errors = 0
        
        for i, row in df.iterrows():
            雌蚊密度 = row['总雌蚊密度（雌性ADI）']
            成蚊汇总_实际 = row['成蚊汇总']
            成蚊汇总_期望 = 雌蚊密度
            
            # 检查是否相等（允许小的浮点误差）
            if abs(成蚊汇总_实际 - 成蚊汇总_期望) > 0.001:
                print(f"❌ 第{i+1}行 成蚊汇总计算错误:")
                print(f"   雌蚊密度: {雌蚊密度}")
                print(f"   实际成蚊汇总: {成蚊汇总_实际}, 期望成蚊汇总: {成蚊汇总_期望}")
                errors += 1
                
                if errors >= 5:  # 只显示前5个错误
                    print("   ... (更多错误)")
                    break
        
        if errors == 0:
            print("✅ 所有成蚊汇总计算都正确")
        else:
            print(f"❌ 发现 {errors} 个成蚊汇总计算错误")
        
        print()
        
        # 显示一些示例计算
        print("=== 示例计算验证 ===")
        has_data = df[df['监测户数'] > 0].head(10)
        
        if len(has_data) > 0:
            print("有监测数据的记录示例:")
            for i, row in has_data.iterrows():
                监测户数 = row['监测户数']
                BI指数 = row['户外布雷图指数（BI）']
                雌蚊密度 = row['总雌蚊密度（雌性ADI）']
                BI汇总 = row['BI汇总']
                成蚊汇总 = row['成蚊汇总']
                
                BI汇总_计算 = BI指数 / 监测户数 * 100 if 监测户数 > 0 else 0
                
                print(f"{row['居委']} - {row['环境类型']}:")
                print(f"  监测户数: {监测户数}, BI指数: {BI指数}, 雌蚊密度: {雌蚊密度}")
                print(f"  BI汇总: {BI汇总} (计算: {BI汇总_计算:.6f})")
                print(f"  成蚊汇总: {成蚊汇总} (应为: {雌蚊密度})")
                print()
        else:
            print("没有找到有监测数据的记录")
        
        # 统计信息
        print("=== 统计信息 ===")
        print(f"总记录数: {len(df)}")
        print(f"有监测数据的记录: {len(df[df['监测户数'] > 0])}")
        print(f"BI汇总 > 0 的记录: {len(df[df['BI汇总'] > 0])}")
        print(f"成蚊汇总 > 0 的记录: {len(df[df['成蚊汇总'] > 0])}")
        
        # BI汇总统计
        bi_stats = df[df['BI汇总'] > 0]['BI汇总']
        if len(bi_stats) > 0:
            print(f"BI汇总统计 (非零值):")
            print(f"  最小值: {bi_stats.min():.2f}")
            print(f"  最大值: {bi_stats.max():.2f}")
            print(f"  平均值: {bi_stats.mean():.2f}")
        
        # 成蚊汇总统计
        mosquito_stats = df[df['成蚊汇总'] > 0]['成蚊汇总']
        if len(mosquito_stats) > 0:
            print(f"成蚊汇总统计 (非零值):")
            print(f"  最小值: {mosquito_stats.min():.2f}")
            print(f"  最大值: {mosquito_stats.max():.2f}")
            print(f"  平均值: {mosquito_stats.mean():.2f}")
        
        return True
        
    except Exception as e:
        print(f"验证过程中出错: {e}")
        return False

def main():
    """
    主函数
    """
    # 验证最新生成的汇总表
    files_to_verify = [
        "汇总表结果_合并单元格处理.xlsx",
        "合并单元格处理结果.xlsx",
        "测试汇总结果.xlsx"
    ]
    
    for file_path in files_to_verify:
        try:
            import os
            if os.path.exists(file_path):
                print(f"\n{'='*60}")
                success = verify_calculations(file_path)
                if success:
                    print("✅ 验证通过")
                else:
                    print("❌ 验证失败")
                print('='*60)
            else:
                print(f"文件不存在: {file_path}")
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")

if __name__ == "__main__":
    main()
