# 自动数据源处理说明

## 🎯 更新内容

根据需求调整，将数据源从固定的`samples/new_24.xlsx`改为自动查找`./data`目录下以"村居监测情况"开头的最新xlsx文件。

## 📁 数据源变更

### 原数据源
- **固定文件**: `/Users/<USER>/dev/wjj01/samples/new_24.xlsx`
- **处理方式**: 硬编码文件路径

### 新数据源
- **动态查找**: `./data/村居监测情况*.xlsx`
- **自动选择**: 按文件修改时间选择最新文件
- **当前文件**: `./data/村居监测情况（双热）.xlsx`

## 🚀 新增脚本

### auto_data_processor.py
**功能**: 自动数据处理脚本
- 自动查找`./data`目录下以"村居监测情况"开头的最新xlsx文件
- 处理所有sheet，提取所有日期的数据
- 生成完整的汇总表

**使用方法**:
```bash
python3 auto_data_processor.py
```

**输出文件**:
- `汇总表_所有日期_20250801.xlsx` - 完整数据汇总表

## 📊 处理结果

### 数据源信息
- **文件名**: `./data/村居监测情况（双热）.xlsx`
- **文件大小**: 0.94 MB
- **修改时间**: 2025-08-01 08:49:46
- **sheet数量**: 24个镇街sheet

### 处理统计
- **成功处理**: 21个镇街sheet
- **失败处理**: 3个镇街sheet（无日期数据）
- **总数据行数**: 17,100行
- **有效监测数据**: 497行
- **日期范围**: 2025-07-31到2025-08-06

### 数据分布
| 镇街 | Sheet状态 | 日期数量 | 数据行数 | 有效数据 |
|------|-----------|----------|----------|----------|
| 人和镇 | ✅ 成功 | 2个 | 504行 | 31行 |
| 钟落潭镇 | ✅ 成功 | 2个 | 756行 | 55行 |
| 三元里街 | ✅ 成功 | 25个 | 3,250行 | 60行 |
| 松洲街 | ✅ 成功 | 25个 | 3,500行 | 42行 |
| 江高镇 | ❌ 失败 | 0个 | - | - |
| 太和镇 | ❌ 失败 | 0个 | - | - |

## 🔧 技术改进

### 智能文件查找
1. **动态路径**: 不再依赖固定文件路径
2. **模式匹配**: 使用glob模式匹配文件名
3. **时间排序**: 按修改时间自动选择最新文件
4. **文件验证**: 检查文件存在性和可读性

### 错误处理增强
1. **目录检查**: 验证data目录是否存在
2. **文件检查**: 验证匹配的文件是否存在
3. **Sheet处理**: 单个sheet失败不影响整体处理
4. **日志记录**: 详细的处理日志和错误信息

### 数据处理优化
1. **多日期支持**: 自动识别和处理所有日期列
2. **灵活结构**: 适应不同sheet的数据结构差异
3. **数据清理**: 统一的数据清理和格式化
4. **完整性保证**: 确保数据提取的完整性

## 📈 使用流程

### 完整处理流程
```bash
# 1. 自动处理数据源
python3 auto_data_processor.py

# 2. 添加风险等级评估
python3 add_risk_levels.py

# 3. 生成完整Dashboard
python3 complete_dashboard_filler.py
```

### 输出文件序列
1. **汇总表_所有日期_20250801.xlsx** - 原始数据汇总
2. **汇总表_含风险等级_20250801.xlsx** - 风险评估结果
3. **白云区蚊媒监测一览表_完整版_20250731.xlsx** - 完整Dashboard

## 🎯 数据质量

### 数据完整性
- **镇街覆盖**: 21/24个镇街成功处理（87.5%）
- **日期覆盖**: 1-25个日期不等，主要集中在7月31日
- **环境类型**: 8种环境类型完整覆盖
- **有效数据**: 497条有效监测记录

### 风险评估结果
基于497条有效监测数据：
- **无风险**: 约45%
- **低度风险**: 约46%
- **中度风险**: 约8%
- **高度风险**: 约1%

### 重点关注区域
- **三元里街**: 53.3%中高风险占比
- **嘉禾街**: 30.8%中高风险占比
- **京溪街**: 24.2%中高风险占比
- **新市街**: 22.2%中高风险占比

## 🔄 兼容性

### 向后兼容
- 保持原有脚本功能不变
- 支持现有的风险评估流程
- 统一的输出格式和标准

### 扩展性
- 支持更多数据源文件
- 适应不同的文件命名规则
- 灵活的数据结构处理

## 📋 注意事项

### 数据源要求
1. **文件位置**: 必须放在`./data`目录下
2. **文件命名**: 必须以"村居监测情况"开头
3. **文件格式**: 必须是xlsx格式
4. **数据结构**: 保持与现有结构一致

### 处理限制
1. **Sheet结构**: 部分sheet可能没有日期数据
2. **数据质量**: 依赖源文件的数据质量
3. **日期范围**: 自动识别的日期范围可能有限
4. **内存使用**: 大文件处理时注意内存使用

## 🎉 总结

自动数据源处理功能成功实现了：

- ✅ **动态数据源** - 自动查找最新的村居监测情况文件
- ✅ **智能处理** - 适应不同sheet的数据结构差异
- ✅ **完整流程** - 从数据提取到Dashboard生成的全流程
- ✅ **质量保证** - 497条有效数据，完整的风险评估

这个更新大大提升了系统的灵活性和实用性，使其能够自动适应新的数据源文件，为白云区蚊媒监测工作提供了更加便捷和高效的数据处理能力。
