#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
遍历所有日期的24镇街表数据处理脚本
为每个日期的每个镇街-居委-环境类型组合生成一条记录
"""

import pandas as pd
import numpy as np
import os
import shutil
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AllDatesProcessor:
    def __init__(self):
        self.today_str = datetime.now().strftime('%Y%m%d')
        
    def excel_serial_to_date(self, serial_number):
        """
        将Excel序列号转换为日期
        """
        excel_epoch = datetime(1900, 1, 1)
        return excel_epoch + timedelta(days=int(serial_number) - 2)
    
    def backup_existing_files(self):
        """
        备份现有的汇总表文件到backup文件夹
        """
        backup_dir = './backup'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
            logger.info(f"创建备份目录: {backup_dir}")
        
        existing_files = []
        for file in os.listdir('.'):
            if file.startswith('汇总表_') and file.endswith('.xlsx'):
                existing_files.append(file)
        
        if existing_files:
            backup_time = datetime.now().strftime('%Y%m%d_%H%M%S')
            for file in existing_files:
                backup_name = file.replace('.xlsx', f'_backup_{backup_time}.xlsx')
                backup_path = os.path.join(backup_dir, backup_name)
                shutil.move(file, backup_path)
                logger.info(f"备份文件: {file} -> {backup_path}")
        else:
            logger.info("没有找到需要备份的汇总表文件")
    
    def find_all_date_columns(self, df_raw):
        """
        查找所有日期列
        """
        if len(df_raw) <= 3:
            return []
            
        # 在第4行查找所有日期
        date_row = df_raw.iloc[3, :]
        dates = []
        for col_idx, value in enumerate(date_row):
            if isinstance(value, (int, float)) and not pd.isna(value):
                if 45800 <= value <= 46000:  # 2025年左右的范围
                    actual_date = self.excel_serial_to_date(value)
                    dates.append({
                        'col_idx': col_idx,
                        'excel_serial': value,
                        'date': actual_date,
                        'date_str': actual_date.strftime('%Y-%m-%d')
                    })
        
        logger.info(f"找到 {len(dates)} 个日期列")
        for date_info in dates:
            logger.info(f"列{date_info['col_idx']}: {date_info['excel_serial']} -> {date_info['date_str']}")
        
        return dates
    
    def extract_monitoring_data_for_date(self, df, date_info):
        """
        提取指定日期的监测数据
        """
        date_col_idx = date_info['col_idx']
        monitoring_cols = {}
        
        # 根据日期列位置确定监测数据列
        if date_col_idx + 2 < len(df.columns):  # 监测户数（跳过地址和人员）
            monitoring_cols['监测户数'] = df.columns[date_col_idx + 2]
        if date_col_idx + 3 < len(df.columns):  # 阳性数
            monitoring_cols['阳性数'] = df.columns[date_col_idx + 3]
        if date_col_idx + 4 < len(df.columns):  # 户外布雷图指数
            monitoring_cols['户外布雷图指数（BI）'] = df.columns[date_col_idx + 4]
        if date_col_idx + 5 < len(df.columns):  # 总雌蚊密度
            monitoring_cols['总雌蚊密度（雌性ADI）'] = df.columns[date_col_idx + 5]
        
        return monitoring_cols
    
    def process_sheet(self, file_path, sheet_name):
        """
        处理单个sheet，提取所有日期的数据
        """
        try:
            logger.info(f"正在处理sheet: {sheet_name}")
            
            # 读取原始数据分析日期列
            df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None, nrows=20)
            
            # 查找所有日期列
            date_columns = self.find_all_date_columns(df_raw)
            if not date_columns:
                logger.error(f"Sheet '{sheet_name}' 中找不到日期列")
                return None
            
            # 读取完整数据，从第6行开始（索引5）作为数据行，跳过第5行的子表头
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=None, skiprows=5)
            
            # 设置列名
            df.columns = ['序号', '监测类型', '居委', '环境类型'] + [f'col_{i}' for i in range(4, len(df.columns))]
            
            # 处理合并单元格 - 向下填充关键字段
            fill_columns = ['序号', '监测类型', '居委']
            for col in fill_columns:
                if col in df.columns:
                    df[col] = df[col].ffill()
            
            # 清理数据
            df = df.dropna(subset=['环境类型'])  # 环境类型不能为空
            
            # 清理环境类型字段
            df['环境类型'] = df['环境类型'].astype(str).str.replace(r'^\d+\.', '', regex=True).str.strip()
            df['环境类型'] = df['环境类型'].str.replace(r'其他场所（闲置房屋或围蔽场所）.*', '其他场所（闲置房屋或围蔽场所）', regex=True)
            
            # 清理居委名称
            df['居委'] = df['居委'].astype(str).str.replace('\n', '').str.strip()
            
            # 添加镇街信息，去掉序号前缀
            clean_town_name = sheet_name
            if '.' in sheet_name and sheet_name[0].isdigit():
                clean_town_name = sheet_name.split('.', 1)[1]
            
            # 为每个日期生成数据
            all_date_data = []
            
            for date_info in date_columns:
                # 提取该日期的监测数据列
                monitoring_cols = self.extract_monitoring_data_for_date(df, date_info)
                
                if not monitoring_cols:
                    logger.warning(f"Sheet '{sheet_name}' 日期 {date_info['date_str']} 找不到监测数据列")
                    continue
                
                # 提取基本信息和该日期的监测数据
                basic_cols = ['序号', '监测类型', '居委', '环境类型']
                extract_cols = basic_cols + list(monitoring_cols.values())
                
                try:
                    date_data = df[extract_cols].copy()
                    
                    # 重命名监测数据列
                    rename_dict = {v: k for k, v in monitoring_cols.items()}
                    date_data = date_data.rename(columns=rename_dict)
                    
                    # 添加日期和镇街信息
                    date_data['镇街'] = clean_town_name
                    date_data['日期'] = date_info['date_str']
                    
                    # 确保数值列为数值类型
                    numeric_cols = ['监测户数', '阳性数', '户外布雷图指数（BI）', '总雌蚊密度（雌性ADI）']
                    for col in numeric_cols:
                        if col in date_data.columns:
                            date_data[col] = pd.to_numeric(date_data[col], errors='coerce').fillna(0)
                    
                    # 添加整体风险等级字段
                    date_data['整体风险等级'] = ''
                    
                    all_date_data.append(date_data)
                    logger.info(f"  日期 {date_info['date_str']}: 提取 {len(date_data)} 行数据")
                    
                except Exception as e:
                    logger.error(f"处理日期 {date_info['date_str']} 时出错: {e}")
                    continue
            
            if all_date_data:
                # 合并所有日期的数据
                sheet_all_data = pd.concat(all_date_data, ignore_index=True)
                logger.info(f"Sheet '{sheet_name}' 处理完成，总计提取 {len(sheet_all_data)} 行数据")
                return sheet_all_data
            else:
                logger.error(f"Sheet '{sheet_name}' 没有提取到任何数据")
                return None
            
        except Exception as e:
            logger.error(f"处理sheet '{sheet_name}' 失败: {e}")
            return None
    
    def process_all_sheets(self, file_path):
        """
        处理所有sheet
        """
        try:
            xl_file = pd.ExcelFile(file_path)
            sheet_names = xl_file.sheet_names
            
            # 过滤掉汇总sheet和其他非镇街sheet
            town_sheets = [name for name in sheet_names if not name.startswith('汇总') and not name.startswith('Wps')]
            
            logger.info(f"发现 {len(town_sheets)} 个镇街sheet")
            
            all_data = []
            successful_sheets = 0
            failed_sheets = []
            
            for i, sheet_name in enumerate(town_sheets, 1):
                print(f"[{i:2d}/{len(town_sheets)}] 处理sheet: {sheet_name}")
                
                sheet_data = self.process_sheet(file_path, sheet_name)
                if sheet_data is not None:
                    all_data.append(sheet_data)
                    successful_sheets += 1
                    
                    # 统计有效数据
                    valid_data = sheet_data[sheet_data['监测户数'] > 0] if '监测户数' in sheet_data.columns else pd.DataFrame()
                    dates_count = sheet_data['日期'].nunique()
                    print(f"    成功提取 {len(sheet_data)} 行数据（{dates_count}个日期），其中有效监测数据: {len(valid_data)} 行")
                else:
                    failed_sheets.append(sheet_name)
                    print(f"    处理失败")
            
            print(f"\n处理完成: 成功 {successful_sheets} 个，失败 {len(failed_sheets)} 个")
            if failed_sheets:
                print("失败的sheet:")
                for sheet in failed_sheets:
                    print(f"  - {sheet}")
            
            return all_data
            
        except Exception as e:
            logger.error(f"处理文件失败: {e}")
            return []

def main():
    """
    主函数
    """
    processor = AllDatesProcessor()
    
    # 输入文件路径
    input_file = '/Users/<USER>/dev/wjj01/samples/new_24.xlsx'
    
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在: {input_file}")
        return
    
    print("遍历所有日期的24镇街表数据提取脚本")
    print("=" * 50)
    print(f"输入文件: {input_file}")
    print("提取策略: 遍历所有日期，每个日期每个镇街每个居委每个环境类型一条记录")
    print()
    
    # 备份现有文件
    processor.backup_existing_files()
    
    # 处理所有sheet
    all_data = processor.process_all_sheets(input_file)
    
    if not all_data:
        print("错误: 没有提取到任何数据")
        return
    
    # 合并所有数据
    print("\n正在合并所有数据...")
    combined_df = pd.concat(all_data, ignore_index=True)
    
    # 生成输出文件名
    output_file = f"汇总表_所有日期_{processor.today_str}.xlsx"
    
    # 保存结果
    try:
        combined_df.to_excel(output_file, index=False)
        print(f"汇总表已保存: {output_file}")
        print(f"总数据行数: {len(combined_df)}")
        
        # 显示统计信息
        print("\n数据统计:")
        print(f"  镇街数量: {combined_df['镇街'].nunique()}")
        print(f"  居委数量: {combined_df['居委'].nunique()}")
        print(f"  环境类型数量: {combined_df['环境类型'].nunique()}")
        print(f"  日期数量: {combined_df['日期'].nunique()}")
        
        # 日期分布
        print("\n日期分布:")
        date_counts = combined_df['日期'].value_counts().sort_index()
        for date, count in date_counts.items():
            print(f"  {date}: {count} 条记录")
        
        # 有效数据统计
        if '监测户数' in combined_df.columns:
            valid_data = combined_df[combined_df['监测户数'] > 0]
            print(f"\n有效监测数据: {len(valid_data)} 行")
            
            # 按日期统计有效数据
            print("各日期有效数据分布:")
            valid_by_date = valid_data['日期'].value_counts().sort_index()
            for date, count in valid_by_date.items():
                print(f"  {date}: {count} 条有效记录")
        
        # 显示前几行数据
        print("\n汇总表预览 (前10行):")
        display_cols = ['镇街', '居委', '环境类型', '日期', '监测户数', '阳性数']
        print(combined_df[display_cols].head(10).to_string(index=False))
        
        print(f"\n处理完成！数据已保存到: {output_file}")
        
    except Exception as e:
        print(f"保存文件失败: {e}")

if __name__ == "__main__":
    main()
