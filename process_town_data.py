#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
镇街表数据汇总脚本
从多个镇街表中读取数据，按居委和环境类型汇总，生成汇总表
"""

import pandas as pd
import numpy as np
import os
import glob
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TownDataProcessor:
    def __init__(self):
        self.summary_columns = [
            '镇街', '居委', '环境类型', '监测户数', '阳性数', 
            '户外布雷图指数（BI）', '总雌蚊密度（雌性ADI）', 'BI汇总', '成蚊汇总'
        ]
        
    def extract_town_name(self, file_path):
        """
        从文件路径或文件内容中提取镇街名称
        """
        # 从文件名中提取
        file_name = os.path.basename(file_path)

        # 常见的镇街名称模式
        town_patterns = [
            r'(\w+镇)', r'(\w+街道)', r'(\w+区)', r'(\w+乡)'
        ]

        for pattern in town_patterns:
            import re
            match = re.search(pattern, file_name)
            if match:
                return match.group(1)

        # 如果文件名中没有找到，尝试从文件内容中提取
        try:
            df_raw = pd.read_excel(file_path, header=None, nrows=5)
            for i in range(5):
                for j in range(min(10, len(df_raw.columns))):
                    cell_value = str(df_raw.iloc[i, j])
                    for pattern in town_patterns:
                        match = re.search(pattern, cell_value)
                        if match:
                            return match.group(1)
        except:
            pass

        return "未知镇街"

    def read_town_file(self, file_path):
        """
        读取单个镇街表文件
        """
        try:
            logger.info(f"正在读取文件: {file_path}")

            # 先读取原始数据，确定表头位置
            df_raw = pd.read_excel(file_path, header=None, nrows=10)

            # 查找表头行（包含'序号', '监测类型', '居委', '环境类型'的行）
            header_row = None
            for i in range(10):
                row_values = [str(x) for x in df_raw.iloc[i, :4]]
                if '序号' in row_values and '监测类型' in row_values and '居委' in row_values and '环境类型' in row_values:
                    header_row = i
                    break

            if header_row is None:
                logger.error(f"无法找到表头行: {file_path}")
                return None, None

            # 读取数据，跳过子表头行
            df = pd.read_excel(file_path, header=header_row, skiprows=[header_row + 1])

            # 清理列名，处理数字列名
            columns = []
            for col in df.columns:
                if isinstance(col, (int, float)) and not pd.isna(col):
                    # 数字列名可能是日期，暂时保留
                    columns.append(str(col))
                else:
                    columns.append(col)
            df.columns = columns

            # 提取镇街名称
            town_name = self.extract_town_name(file_path)

            logger.info(f"成功读取文件: {file_path}, 数据行数: {len(df)}, 镇街: {town_name}")
            return df, town_name

        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {str(e)}")
            return None, None
    
    def extract_basic_info(self, df):
        """
        从镇街表中提取基本信息（序号、监测类型、居委、环境类型）
        """
        if df is None:
            return None
            
        # 基本列
        basic_cols = ['序号', '监测类型', '居委', '环境类型']
        
        # 检查必要列是否存在
        missing_cols = [col for col in basic_cols if col not in df.columns]
        if missing_cols:
            logger.error(f"缺少必要列: {missing_cols}")
            return None
        
        # 提取基本信息
        basic_df = df[basic_cols].copy()
        
        # 清理数据
        basic_df = basic_df.dropna(subset=['居委', '环境类型'])
        
        # 清理居委名称（去除换行符等）
        basic_df['居委'] = basic_df['居委'].astype(str).str.replace('\n', '').str.strip()
        
        return basic_df
    
    def extract_monitoring_data(self, df, date_columns, town_name):
        """
        从镇街表中提取监测数据
        根据镇街表的结构，每个日期列后面跟着几列监测数据
        """
        if df is None:
            return None

        monitoring_data = []

        # 基本信息列
        basic_cols = ['序号', '监测类型', '居委', '环境类型']

        # 获取所有列名的索引映射
        col_index_map = {col: i for i, col in enumerate(df.columns)}

        # 对每个日期列，提取其后面的监测数据列
        for date_col in date_columns:
            date_str = str(date_col)

            # 找到日期列的索引
            if date_col not in col_index_map:
                continue

            date_col_index = col_index_map[date_col]

            # 日期列后面通常跟着几列监测数据，我们检查后面的5-10列
            monitoring_cols = {}

            # 检查日期列后面的列
            for offset in range(1, 10):  # 检查后面9列
                if date_col_index + offset >= len(df.columns):
                    break

                next_col = df.columns[date_col_index + offset]
                next_col_str = str(next_col).lower()

                # 根据列名或者位置推断数据类型
                if offset == 1:  # 第一列通常是监测具体地址
                    continue
                elif offset == 2:  # 第二列通常是监测户数
                    monitoring_cols['监测户数'] = next_col
                elif offset == 3:  # 第三列通常是阳性数
                    monitoring_cols['阳性数'] = next_col
                elif offset == 4:  # 第四列通常是户外布雷图指数
                    monitoring_cols['户外布雷图指数（BI）'] = next_col
                elif offset == 5:  # 第五列通常是总雌蚊密度
                    monitoring_cols['总雌蚊密度（雌性ADI）'] = next_col
                elif '监测户数' in next_col_str or '户数' in next_col_str:
                    monitoring_cols['监测户数'] = next_col
                elif '阳性数' in next_col_str:
                    monitoring_cols['阳性数'] = next_col
                elif '户外布雷图指数' in next_col_str or 'bi' in next_col_str:
                    monitoring_cols['户外布雷图指数（BI）'] = next_col
                elif '总雌蚊密度' in next_col_str or 'adi' in next_col_str:
                    monitoring_cols['总雌蚊密度（雌性ADI）'] = next_col

            if monitoring_cols:
                # 提取该日期的数据
                date_data = df[basic_cols + list(monitoring_cols.values())].copy()
                date_data['日期'] = date_str
                date_data['镇街'] = town_name

                # 重命名列
                rename_dict = {v: k for k, v in monitoring_cols.items()}
                date_data = date_data.rename(columns=rename_dict)

                monitoring_data.append(date_data)

        if monitoring_data:
            return pd.concat(monitoring_data, ignore_index=True)
        else:
            return None
    
    def find_date_columns(self, df):
        """
        查找日期相关的列
        """
        date_columns = []
        for col in df.columns:
            # 检查是否为数字（可能是Excel日期序列号）
            if isinstance(col, (int, float)) and not pd.isna(col):
                # Excel日期序列号通常在40000-50000范围内（对应2009-2037年）
                # 45869对应2025年左右的日期
                if 40000 <= col <= 50000:
                    date_columns.append(col)
            # 也检查字符串形式的数字
            elif isinstance(col, str) and col.isdigit():
                col_num = int(col)
                if 40000 <= col_num <= 50000:
                    date_columns.append(col)

        return date_columns
    
    def aggregate_data(self, all_data):
        """
        汇总所有数据
        """
        if not all_data:
            logger.error("没有数据可以汇总")
            return None
        
        # 合并所有数据
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # 清理数据
        combined_df = combined_df.dropna(subset=['居委', '环境类型'])
        combined_df['居委'] = combined_df['居委'].astype(str).str.replace('\n', '').str.strip()
        
        # 按居委和环境类型分组汇总
        numeric_cols = ['监测户数', '阳性数', '户外布雷图指数（BI）', '总雌蚊密度（雌性ADI）']
        
        # 确保数值列为数值类型
        for col in numeric_cols:
            if col in combined_df.columns:
                combined_df[col] = pd.to_numeric(combined_df[col], errors='coerce')
        
        # 分组汇总
        agg_dict = {}
        for col in numeric_cols:
            if col in combined_df.columns:
                agg_dict[col] = 'sum'
        
        if not agg_dict:
            logger.error("没有找到可汇总的数值列")
            return None
        
        # 检查是否有镇街列，如果没有则添加默认值
        if '镇街' in combined_df.columns:
            agg_dict['镇街'] = 'first'

        summary_df = combined_df.groupby(['居委', '环境类型']).agg(agg_dict).reset_index()

        # 如果没有镇街列，添加默认值
        if '镇街' not in summary_df.columns:
            summary_df['镇街'] = '未知镇街'
        
        # 计算汇总字段（在最后计算，不是累加）
        if '户外布雷图指数（BI）' in summary_df.columns and '监测户数' in summary_df.columns:
            # BI汇总 = 户外布雷图指数（BI） / 监测户数 * 100
            # 当监测户数为0时，BI汇总为0
            summary_df['BI汇总'] = np.where(
                summary_df['监测户数'] > 0,
                summary_df['户外布雷图指数（BI）'] / summary_df['监测户数'] * 100,
                0
            )
        else:
            summary_df['BI汇总'] = 0

        if '总雌蚊密度（雌性ADI）' in summary_df.columns:
            # 成蚊汇总就是总雌蚊密度的值
            summary_df['成蚊汇总'] = summary_df['总雌蚊密度（雌性ADI）']
        else:
            summary_df['成蚊汇总'] = 0
        
        # 重新排列列顺序
        final_cols = ['镇街', '居委', '环境类型', '监测户数', '阳性数', 
                     '户外布雷图指数（BI）', '总雌蚊密度（雌性ADI）', 'BI汇总', '成蚊汇总']
        
        existing_cols = [col for col in final_cols if col in summary_df.columns]
        summary_df = summary_df[existing_cols]
        
        return summary_df
    
    def process_files(self, input_pattern, output_file):
        """
        处理所有文件并生成汇总表
        """
        # 查找所有匹配的文件
        files = glob.glob(input_pattern)
        
        if not files:
            logger.error(f"没有找到匹配的文件: {input_pattern}")
            return False
        
        logger.info(f"找到 {len(files)} 个文件")
        
        all_data = []
        
        for file_path in files:
            # 读取文件
            df, town_name = self.read_town_file(file_path)
            if df is None:
                continue

            # 查找日期列
            date_columns = self.find_date_columns(df)
            logger.info(f"文件 {file_path} 中找到日期列: {date_columns}")

            # 提取监测数据
            monitoring_data = self.extract_monitoring_data(df, date_columns, town_name)

            if monitoring_data is not None:
                all_data.append(monitoring_data)
            else:
                logger.warning(f"文件 {file_path} 没有提取到有效的监测数据")
        
        if not all_data:
            logger.error("没有提取到任何有效数据")
            return False
        
        # 汇总数据
        summary_df = self.aggregate_data(all_data)
        
        if summary_df is None:
            logger.error("数据汇总失败")
            return False
        
        # 保存结果
        try:
            summary_df.to_excel(output_file, index=False)
            logger.info(f"汇总表已保存到: {output_file}")
            logger.info(f"汇总表包含 {len(summary_df)} 行数据")
            return True
        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")
            return False

def main():
    """
    主函数
    """
    processor = TownDataProcessor()
    
    # 配置输入和输出路径
    input_pattern = "data/镇街表*.xlsx"  # 修改为实际的文件路径模式
    output_file = "汇总表结果.xlsx"
    
    # 处理文件
    success = processor.process_files(input_pattern, output_file)
    
    if success:
        print("数据处理完成！")
    else:
        print("数据处理失败，请检查日志信息。")

if __name__ == "__main__":
    main()
