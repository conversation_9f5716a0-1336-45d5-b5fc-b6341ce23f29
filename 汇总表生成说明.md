# 白云区"两热"媒介伊蚊应急监测一览表生成说明

## 📋 功能概述

基于明细监测数据，自动生成符合标准格式的"两热"媒介伊蚊应急监测一览表，包含：
- **全区监测评估情况** - 按镇街和村居汇总的整体监测数据
- **重点场所监测情况** - 按8类重点场所分类的详细监测数据

## 🚀 使用方法

### 前置条件

确保已有包含风险等级的明细数据文件：
```bash
# 如果没有风险等级数据，先运行风险评估
python3 add_risk_levels.py
```

### 生成汇总表

```bash
# 运行汇总表生成脚本
python3 generate_summary_table.py
```

### 输出文件

- **两热媒介伊蚊应急监测一览表_20250801.xlsx** - 标准格式的汇总表

## 📊 表格结构

### 全区监测评估情况部分

| 字段名 | 说明 | 计算方式 |
|--------|------|----------|
| 镇街 | 镇街名称 | 从明细数据提取 |
| 村居 | 村居名称 | 从明细数据提取 |
| 监测类型 | 监测频次类型 | 一类/二类/三类 |
| 居委会总数 | 该镇街居委数量 | 自动统计 |
| 监测户数 | 总监测户数 | 所有环境类型监测户数之和 |
| 低度风险个数 | 低度风险记录数 | 统计风险级别为"低度风险"的记录数 |
| 中度风险个数 | 中度风险记录数 | 统计风险级别为"中度风险"的记录数 |
| 高度风险个数 | 高度风险记录数 | 统计风险级别为"高度风险"的记录数 |
| 低度风险占比 | 低度风险百分比 | 低度风险个数 / 总风险记录数 × 100% |
| 中度风险占比 | 中度风险百分比 | 中度风险个数 / 总风险记录数 × 100% |
| 高度风险占比 | 高度风险百分比 | 高度风险个数 / 总风险记录数 × 100% |
| 监测阳性数 | 总阳性数 | 所有环境类型阳性数之和 |
| 监测阳性率 | 阳性率百分比 | 监测阳性数 / 监测户数 × 100% |

### 重点场所监测情况部分

针对8类重点场所，每类包含4个字段：

| 场所类型 | 字段说明 |
|----------|----------|
| **1. 物业小区** | 监测户数、低度风险占比、中度风险占比、高度风险占比 |
| **2. 建筑工地** | 监测户数、低度风险占比、中度风险占比、高度风险占比 |
| **3. 公园景区** | 监测户数、低度风险占比、中度风险占比、高度风险占比 |
| **4. 学校** | 监测户数、低度风险占比、中度风险占比、高度风险占比 |
| **5. 福利机构** | 监测户数、低度风险占比、中度风险占比、高度风险占比 |
| **6. 医疗机构** | 监测户数、低度风险占比、中度风险占比、高度风险占比 |
| **7. 农贸市场** | 监测户数、低度风险占比、中度风险占比、高度风险占比 |
| **8. 其它场所** | 监测户数、低度风险占比、中度风险占比、高度风险占比 |

## 📈 数据处理逻辑

### 汇总层级

1. **全区汇总** - 所有镇街所有村居的数据汇总
2. **镇街汇总** - 单个镇街下所有村居的数据汇总  
3. **村居详细** - 单个村居的详细监测数据

### 风险占比计算

```python
# 对于每个村居或场所
风险记录总数 = 低度风险个数 + 中度风险个数 + 高度风险个数
低度风险占比 = 低度风险个数 / 风险记录总数 × 100%
中度风险占比 = 中度风险个数 / 风险记录总数 × 100%
高度风险占比 = 高度风险个数 / 风险记录总数 × 100%
```

### 重点场所数据

- **监测户数**: 该场所类型的监测户数总和
- **风险占比**: 该场所类型下不同风险级别的占比
- **空值处理**: 无监测数据的场所各项指标均为0

## 🎯 生成结果

### 数据规模

- **总行数**: 346行（包含全区、镇街、村居三级数据）
- **总列数**: 45列（基本信息13列 + 重点场所32列）
- **覆盖范围**: 22个镇街，346个村居，8类重点场所

### 数据质量

- **完整性**: 每个村居都有对应记录
- **准确性**: 基于明细数据精确计算
- **一致性**: 统一的数据格式和计算标准

### 风险分布

- **有低度风险记录**: 153条
- **有中度风险记录**: 31条  
- **有高度风险记录**: 4条
- **重点场所覆盖**: 物业小区234条记录

## 📋 使用示例

### 填表指导

生成的汇总表可直接用于填写正式的监测一览表：

1. **全区监测评估情况**
   - 从表格中找到"全区情况"行
   - 复制对应的监测户数、风险占比、阳性率等数据

2. **镇街汇总数据**
   - 从表格中找到对应镇街行（村居列为空的记录）
   - 复制镇街级别的汇总数据

3. **村居详细数据**
   - 从表格中找到具体村居行
   - 复制该村居的详细监测数据

4. **重点场所数据**
   - 从对应的重点场所列中获取数据
   - 如"1. 物业小区_监测户数"、"1. 物业小区_低度风险占比"等

### 数据验证

- **数据一致性**: 村居数据汇总应等于镇街数据
- **占比合理性**: 各风险占比之和应为100%（有风险记录的情况下）
- **户数匹配**: 重点场所监测户数之和应等于总监测户数

## 🔧 技术特点

### 自动化处理

- **智能识别**: 自动识别明细数据中的关键字段
- **分类汇总**: 按镇街、村居、场所类型自动分类统计
- **格式标准化**: 生成符合标准格式的汇总表

### 数据映射

- **环境类型映射**: 自动将明细数据中的环境类型映射到8类重点场所
- **风险级别统计**: 基于明细数据中的风险级别字段进行统计
- **监测类型识别**: 自动识别和标准化监测类型

### 质量保证

- **数据验证**: 自动验证数据完整性和一致性
- **异常处理**: 处理空值和异常数据
- **格式统一**: 确保数值格式的一致性

## 📝 注意事项

### 数据要求

1. **必需字段**: 镇街、居委、环境类型、监测户数、阳性数、风险级别
2. **数据格式**: Excel格式，字段名称需与系统一致
3. **风险级别**: 必须包含风险等级评估结果

### 使用建议

1. **数据核对**: 生成后建议核对关键数据的准确性
2. **格式调整**: 可根据实际需要调整表格格式和样式
3. **定期更新**: 随着监测数据更新，定期重新生成汇总表

### 常见问题

1. **风险占比为0**: 可能是该记录没有风险级别数据
2. **监测户数不匹配**: 检查明细数据中的环境类型分类
3. **镇街汇总异常**: 确认明细数据中镇街名称的一致性

## 🎉 总结

汇总表生成脚本实现了：

- ✅ **自动化生成** - 基于明细数据自动生成标准格式汇总表
- ✅ **完整覆盖** - 包含全区、镇街、村居三级数据
- ✅ **重点场所** - 8类重点场所的详细监测数据
- ✅ **风险评估** - 基于风险级别的科学统计分析
- ✅ **标准格式** - 符合正式监测一览表的格式要求

这个系统为"两热"媒介伊蚊应急监测工作提供了高效、准确的数据汇总工具，大大提升了报表生成的效率和质量。
