# 一览表数据统计核心逻辑

## 1. 数据流程概览

```
汇总表数据 → 风险等级计算 → 居委风险统计 → 一览表填写
```

## 2. 核心统计逻辑

### 2.1 居委风险等级判定

每个居委的风险等级按该居委所有监测记录中的**最高风险**确定：

```python
def get_community_risk_level(community_data):
    """获取居委的整体风险等级"""
    risk_levels = community_data['风险级别'].values
    
    if '高度风险' in risk_levels:
        return '高度风险'
    elif '中度风险' in risk_levels:
        return '中度风险'
    elif '低度风险' in risk_levels:
        return '低度风险'
    else:
        return '无风险'
```

### 2.2 镇街统计指标计算

#### 监测村居数（E列）
```python
监测村居数 = 该镇街的唯一居委数量
```

#### 中高风险占比（F列）
```python
中高风险居委数 = 高度风险居委数 + 中度风险居委数
中高风险占比 = (中高风险居委数 / 总居委数) × 100%
```

#### 风险分布（G-J列）
```python
G列(高风险) = 高度风险居委数
H列(中风险) = 中度风险居委数  
I列(低风险) = 低度风险居委数
J列(风险可控) = 无风险居委数
```

### 2.3 环境类型统计

对每种环境类型（物业小区、农贸市场等），统计逻辑相同：

```python
def analyze_environment_type(df, town_name, env_type):
    """分析特定环境类型的风险分布"""
    # 1. 筛选该镇街该环境类型的数据
    env_data = df[
        (df['镇街'] == town_name) & 
        (df['环境类型'].str.contains(env_type))
    ]
    
    # 2. 按居委统计该环境类型的风险
    community_risks = {}
    for community in env_data['居委'].unique():
        community_env_data = env_data[env_data['居委'] == community]
        community_risks[community] = get_community_risk_level(community_env_data)
    
    # 3. 统计各风险等级数量
    total = len(community_risks)
    high_count = sum(1 for r in community_risks.values() if r == '高度风险')
    medium_count = sum(1 for r in community_risks.values() if r == '中度风险')
    
    # 4. 计算中高风险占比
    medium_high_count = high_count + medium_count
    risk_pct = (medium_high_count / total) * 100 if total > 0 else 0
    
    return {
        'total': total,           # 总数
        'risk_pct': risk_pct,     # 中高风险占比
        'high': high_count,       # 高风险数
        'medium': medium_count    # 中风险数
    }
```

## 3. 实际统计示例

### 3.1 三元里街农贸市场统计

**汇总表原始数据**：
```
镇街: 三元里街, 居委: 松柏居委, 环境类型: 农贸市场, 风险级别: 中度风险
镇街: 三元里街, 居委: 飞鹅居委, 环境类型: 农贸市场, 风险级别: 无风险
镇街: 三元里街, 居委: 梓元岗居委, 环境类型: 农贸市场, 风险级别: 无风险
... (共13个居委的农贸市场数据)
```

**统计过程**：
1. 筛选三元里街农贸市场数据：13条记录
2. 按居委统计风险：
   - 松柏居委：中度风险
   - 其他12个居委：无风险
3. 计算结果：
   - 总数：13个居委
   - 中高风险：1个居委
   - 中高风险占比：1/13 = 7.7%

**一览表填写**：
```
AK21(农贸市场总数): 13
AL21(农贸市场中高风险占比): 7.7%
AM21(农贸市场高风险): 0
AN21(农贸市场中风险): 1
```

### 3.2 钟落潭镇整体统计

**汇总表数据**：
- 42个居委，每个居委9种环境类型
- 总计378条监测记录
- 风险分布：30个中度风险居委 + 12个低度风险居委

**统计过程**：
1. 按居委统计风险等级：
   ```python
   community_risks = {
       '钟一村委': '中度风险',
       '钟二村委': '中度风险',
       ...  # 30个中度风险居委
       '光明村委': '低度风险',
       ...  # 12个低度风险居委
   }
   ```

2. 计算统计指标：
   ```python
   总居委数 = 42
   高风险居委数 = 0
   中风险居委数 = 30
   低风险居委数 = 12
   无风险居委数 = 0
   中高风险占比 = (0 + 30) / 42 × 100% = 71.4%
   ```

**一览表填写**：
```
E18(监测村居数): 42
F18(中高风险占比): 71.4%
G18(高风险): 0
H18(中风险): 30
I18(低风险): 12
J18(风险可控): 0
K18(监测场所数): 378
```

## 4. 监测类型归类

### 4.1 归类规则

```python
def classify_monitoring_type(monitoring_type):
    """监测类型归类"""
    if '一类' in monitoring_type or '每天1测' in monitoring_type:
        return '一类'
    elif '三类' in monitoring_type or '三天1测' in monitoring_type:
        return '三类'
    else:
        return '二类'  # 其他类型默认为二类
```

### 4.2 具体归类

| 汇总表监测类型 | 归类结果 | 填写位置 |
|---------------|----------|----------|
| 一类（每天1测） | 一类 | 起始行 |
| 涉疫（每天1测） | 一类 | 起始行 |
| 三类（三天1测） | 三类 | 起始行+2 |
| 非涉疫（三天1测） | 三类 | 起始行+2 |

### 4.3 填写行计算

```python
def get_fill_row(town_start_row, monitoring_classification):
    """计算填写行号"""
    if monitoring_classification == '一类':
        return town_start_row
    elif monitoring_classification == '二类':
        return town_start_row + 1
    else:  # 三类
        return town_start_row + 2
```

## 5. 特殊处理

### 5.1 钟落潭镇数据修正

钟落潭镇的Excel列结构特殊，需要特殊处理：

```python
def fix_zhongluotan_data(data):
    """修正钟落潭镇数据"""
    # 特殊列映射
    data['监测户数'] = data['阳性数']  # 原"阳性数"列实际是监测户数
    data['阳性数'] = round(data['户外布雷图指数（BI）'] * data['监测户数'] / 100)
    
    return data
```

### 5.2 缺失数据处理

```python
def handle_missing_data(town_stats, town_name):
    """处理缺失数据"""
    if town_name not in town_stats:
        return {
            'communities': 0,
            'high_risk': 0,
            'medium_risk': 0,
            'low_risk': 0,
            'no_risk': 0,
            'medium_high_risk_pct': 0,
            'total_sites': 0
        }
    return town_stats[town_name]
```

## 6. 数据验证

### 6.1 计算验证

```python
def validate_risk_percentage(total, high, medium, percentage):
    """验证中高风险占比计算"""
    expected = ((high + medium) / total) * 100 if total > 0 else 0
    actual = float(percentage.replace('%', ''))
    
    return abs(expected - actual) < 0.1
```

### 6.2 数据合理性检查

```python
def check_data_reasonableness(data):
    """检查数据合理性"""
    checks = []
    
    # 检查阳性数是否为整数
    if data['阳性数'] != int(data['阳性数']):
        checks.append("阳性数应为整数")
    
    # 检查阳性率是否合理
    if data['监测户数'] > 0:
        rate = data['阳性数'] / data['监测户数'] * 100
        if rate > 100:
            checks.append("阳性率超过100%")
    
    return checks
```

## 7. 关键要点

1. **居委风险等级**：按该居委所有记录的最高风险确定
2. **中高风险占比**：(高风险+中风险居委数) / 总居委数
3. **环境类型统计**：每种环境类型独立统计居委风险分布
4. **监测类型归类**：按监测频率归类（每天1测→一类，三天1测→三类）
5. **数据一致性**：汇总表和一览表必须基于同一日期
6. **特殊处理**：钟落潭镇需要特殊的列映射处理

---

*核心逻辑说明 - 白云区蚊媒监测数据处理系统*
