# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
# 临时文件
temp_*.xlsx
*_unmerged.xlsx
~$*.xlsx
~$*.xls

# 输出文件（保留示例，排除实际输出）
汇总表_*.xlsx
!汇总表示例.xlsx
测试汇总结果.xlsx
合并单元格处理结果.xlsx

# 备份文件夹内容（保留文件夹结构）
backup/*.xlsx

# 日志文件
*.log

# 数据文件（如果有敏感数据）
# data/*.xlsx

# Jupyter Notebook
.ipynb_checkpoints

# pytest
.pytest_cache/
.coverage

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
