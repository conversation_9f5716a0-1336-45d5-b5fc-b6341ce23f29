#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
填写dashboard.xlsx模板脚本
将汇总表数据填写到dashboard模板的对应单元格中
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime
import logging
import openpyxl
from openpyxl import load_workbook
import shutil

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DashboardFiller:
    def __init__(self):
        self.dashboard_template = '/Users/<USER>/dev/wjj01/samples/dashboard.xlsx'
        
        # 镇街映射表（按dashboard中的实际顺序）
        self.town_mapping = {
            '江高镇': {'row_start': 9},
            '人和镇': {'row_start': 12},
            '太和镇': {'row_start': 15},
            '钟落潭镇': {'row_start': 18},
            '三元里街': {'row_start': 21},
            '松洲街': {'row_start': 24},
            '景泰街': {'row_start': 27},
            '黄石街': {'row_start': 30},
            '同德街': {'row_start': 33},
            '棠景街': {'row_start': 36},
            '新市街': {'row_start': 39},
            '同和街': {'row_start': 42},
            '京溪街': {'row_start': 45},
            '永平街': {'row_start': 48},
            '均禾街': {'row_start': 51},
            '嘉禾街': {'row_start': 54},
            '石井街': {'row_start': 57},
            '鹤龙街': {'row_start': 60},
            '云城街': {'row_start': 63},
            '白云湖街': {'row_start': 69},
            '石门街': {'row_start': 72},
            '龙归街': {'row_start': 75},
            '大源街': {'row_start': 78},
            '金沙街': {'row_start': 81}
        }
        
        # 列映射
        self.column_mapping = {
            'monitoring_type': 4,  # D列：监测类型
            'monitored_communities': 5,  # E列：监测村居数
            'medium_high_risk_pct': 6,  # F列：中高风险占比
            'high_risk': 7,  # G列：高风险
            'medium_risk': 8,  # H列：中风险
            'low_risk': 9,  # I列：低风险
            'no_risk': 10,  # J列：风险可控
            'monitoring_sites': 11,  # K列：监测场所数
            'site_risk_pct': 12,  # L列：中高风险占比
            'residential_total': 13,  # M列：物业小区总数
            'residential_risk_pct': 14  # N列：物业小区中高风险占比
        }
    
    def find_latest_summary_file(self):
        """查找最新的汇总表文件"""
        pattern = "汇总表_含风险等级_*.xlsx"
        files = glob.glob(pattern)
        
        if not files:
            pattern = "汇总表_*.xlsx"
            files = glob.glob(pattern)
        
        if not files:
            logger.error("没有找到汇总表文件")
            return None
        
        latest_file = max(files, key=os.path.getmtime)
        logger.info(f"找到最新汇总表文件: {latest_file}")
        return latest_file
    
    def extract_date_from_filename(self, filename):
        """从文件名中提取日期"""
        import re
        match = re.search(r'(\d{8})', filename)
        if match:
            date_str = match.group(1)
            try:
                date_obj = datetime.strptime(date_str, '%Y%m%d')
                return date_obj.strftime('%Y-%m-%d')
            except:
                pass
        return datetime.now().strftime('%Y-%m-%d')
    
    def analyze_summary_data(self, summary_file, target_date='2025-07-31'):
        """分析汇总表数据"""
        try:
            df = pd.read_excel(summary_file)
            logger.info(f"读取汇总表: {summary_file}, 数据行数: {len(df)}")
            
            # 筛选指定日期的数据
            if '日期' in df.columns:
                df = df[df['日期'] == target_date]
                logger.info(f"筛选日期 {target_date} 的数据: {len(df)} 行")
            
            if len(df) == 0:
                logger.error("没有找到指定日期的数据")
                return None
            
            # 按镇街统计
            town_stats = {}
            for town in df['镇街'].unique():
                town_data = df[df['镇街'] == town]
                
                # 统计监测类型
                monitoring_types = []
                if '监测类型' in town_data.columns:
                    types = town_data['监测类型'].unique()
                    for t in types:
                        if '一类' in str(t) or '每天' in str(t):
                            monitoring_types.append('一类')
                        elif '二类' in str(t):
                            monitoring_types.append('二类')
                        elif '三类' in str(t) or '三天' in str(t):
                            monitoring_types.append('三类')
                
                # 风险统计
                risk_counts = {'无风险': 0, '低度风险': 0, '中度风险': 0, '高度风险': 0}
                if '风险级别' in town_data.columns:
                    for risk in town_data['风险级别'].value_counts().items():
                        if risk[0] in risk_counts:
                            risk_counts[risk[0]] = risk[1]
                
                # 环境类型统计
                env_stats = {}
                for env_type in ['物业小区', '建筑工地', '公园景区', '学校', '福利机构', '医疗机构', '农贸市场', '其他场所（闲置房屋或围蔽场所）']:
                    env_data = town_data[town_data['环境类型'].str.contains(env_type, na=False)]
                    if len(env_data) == 0:
                        # 模糊匹配
                        for col_env in town_data['环境类型'].unique():
                            if env_type in str(col_env) or str(col_env) in env_type:
                                env_data = town_data[town_data['环境类型'] == col_env]
                                break
                    
                    env_risk_counts = {'无风险': 0, '低度风险': 0, '中度风险': 0, '高度风险': 0}
                    if len(env_data) > 0 and '风险级别' in env_data.columns:
                        for risk in env_data['风险级别'].value_counts().items():
                            if risk[0] in env_risk_counts:
                                env_risk_counts[risk[0]] = risk[1]
                    
                    env_stats[env_type] = {
                        'total': len(env_data),
                        'monitoring_points': env_data['监测户数'].sum() if '监测户数' in env_data.columns else 0,
                        'risks': env_risk_counts
                    }
                
                town_stats[town] = {
                    'monitoring_types': monitoring_types,
                    'communities': town_data['居委'].nunique(),
                    'total_records': len(town_data),
                    'monitoring_points': town_data['监测户数'].sum() if '监测户数' in town_data.columns else 0,
                    'risks': risk_counts,
                    'environments': env_stats
                }
            
            # 全区统计
            overall_risks = {'无风险': 0, '低度风险': 0, '中度风险': 0, '高度风险': 0}
            if '风险级别' in df.columns:
                for risk in df['风险级别'].value_counts().items():
                    if risk[0] in overall_risks:
                        overall_risks[risk[0]] = risk[1]
            
            overall_stats = {
                'total_communities': df['居委'].nunique(),
                'total_monitoring_points': df['监测户数'].sum() if '监测户数' in df.columns else 0,
                'risks': overall_risks
            }
            
            return {
                'overall': overall_stats,
                'by_town': town_stats
            }
            
        except Exception as e:
            logger.error(f"分析汇总表数据失败: {e}")
            return None
    
    def fill_dashboard(self, summary_file, target_date=None):
        """填写dashboard数据"""
        try:
            # 分析数据
            if target_date is None:
                target_date = '2025-07-31'  # 使用有数据的日期
            
            analysis = self.analyze_summary_data(summary_file, target_date)
            if analysis is None:
                return False
            
            # 生成输出文件名
            date_str = target_date.replace('-', '')
            output_file = f"白云区蚊媒监测一览表_{date_str}.xlsx"
            
            # 复制模板文件
            shutil.copy2(self.dashboard_template, output_file)
            
            # 使用openpyxl编辑文件
            wb = load_workbook(output_file)
            ws = wb.active
            
            # 更新标题
            title_cell = ws['A1']
            if title_cell.value:
                title_cell.value = f"白云区登革热和基孔肯雅热媒介伊蚊应急监测一览表（{target_date}）"
            
            # 填写全区数据（第6-8行）
            overall = analysis['overall']
            
            # 全区一类监测数据（第6行）
            ws.cell(row=6, column=5, value=overall['total_communities'])  # 监测村居数
            medium_high_risk = overall['risks']['中度风险'] + overall['risks']['高度风险']
            total_risk_records = sum(overall['risks'].values())
            if total_risk_records > 0:
                ws.cell(row=6, column=6, value=f"{(medium_high_risk/total_risk_records)*100:.1f}%")  # 中高风险占比
            ws.cell(row=6, column=7, value=overall['risks']['高度风险'])  # 高风险
            ws.cell(row=6, column=8, value=overall['risks']['中度风险'])  # 中风险
            ws.cell(row=6, column=9, value=overall['risks']['低度风险'])  # 低风险
            ws.cell(row=6, column=10, value=overall['risks']['无风险'])  # 风险可控
            
            # 填写各镇街数据
            town_stats = analysis['by_town']
            for town_name, town_data in town_stats.items():
                if town_name in self.town_mapping:
                    mapping = self.town_mapping[town_name]
                    start_row = mapping['row_start']
                    
                    # 确定主要监测类型
                    main_type = '三类'  # 默认
                    if town_data['monitoring_types']:
                        if '一类' in town_data['monitoring_types']:
                            main_type = '一类'
                        elif '二类' in town_data['monitoring_types']:
                            main_type = '二类'
                    
                    # 根据监测类型确定填写的行
                    if main_type == '一类':
                        fill_row = start_row
                    elif main_type == '二类':
                        fill_row = start_row + 1
                    else:  # 三类
                        fill_row = start_row + 2
                    
                    # 填写数据
                    ws.cell(row=fill_row, column=5, value=town_data['communities'])  # 监测村居数
                    
                    # 计算中高风险占比
                    medium_high = town_data['risks']['中度风险'] + town_data['risks']['高度风险']
                    total_risks = sum(town_data['risks'].values())
                    if total_risks > 0:
                        ws.cell(row=fill_row, column=6, value=f"{(medium_high/total_risks)*100:.1f}%")
                    
                    ws.cell(row=fill_row, column=7, value=town_data['risks']['高度风险'])  # 高风险
                    ws.cell(row=fill_row, column=8, value=town_data['risks']['中度风险'])  # 中风险
                    ws.cell(row=fill_row, column=9, value=town_data['risks']['低度风险'])  # 低风险
                    ws.cell(row=fill_row, column=10, value=town_data['risks']['无风险'])  # 风险可控
                    
                    # 监测场所数
                    total_sites = sum([env['total'] for env in town_data['environments'].values()])
                    ws.cell(row=fill_row, column=11, value=total_sites)
                    
                    # 物业小区数据
                    if '物业小区' in town_data['environments']:
                        residential = town_data['environments']['物业小区']
                        ws.cell(row=fill_row, column=13, value=residential['total'])  # 物业小区总数
                        
                        # 物业小区中高风险占比
                        res_medium_high = residential['risks']['中度风险'] + residential['risks']['高度风险']
                        res_total = sum(residential['risks'].values())
                        if res_total > 0:
                            ws.cell(row=fill_row, column=14, value=f"{(res_medium_high/res_total)*100:.1f}%")
            
            # 保存文件
            wb.save(output_file)
            wb.close()
            
            logger.info(f"Dashboard填写完成: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"填写Dashboard失败: {e}")
            return False

def main():
    """主函数"""
    filler = DashboardFiller()
    
    print("Dashboard填写脚本")
    print("=" * 40)
    
    # 查找最新的汇总表文件
    summary_file = filler.find_latest_summary_file()
    if summary_file is None:
        print("错误: 没有找到汇总表文件")
        return
    
    print(f"输入文件: {summary_file}")
    
    # 填写dashboard
    success = filler.fill_dashboard(summary_file)
    
    if success:
        print(f"\nDashboard填写成功！")
        print(f"输出文件: 白云区蚊媒监测一览表_20250731.xlsx")
    else:
        print("\nDashboard填写失败，请检查错误信息")

if __name__ == "__main__":
    main()
